Metadata-Version: 2.2
Name: cssselect
Version: 1.3.0
Summary: cssselect parses CSS3 Selectors and translates them to XPath 1.0
Home-page: https://github.com/scrapy/cssselect
Author: <PERSON>icking
Author-email: <EMAIL>
Maintainer: <PERSON>
Maintainer-email: <EMAIL>
License: BSD
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: AUTHORS
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: maintainer
Dynamic: maintainer-email
Dynamic: requires-python
Dynamic: summary


===================================
cssselect: CSS Selectors for Python
===================================

.. image:: https://img.shields.io/pypi/v/cssselect.svg
   :target: https://pypi.python.org/pypi/cssselect
   :alt: PyPI Version

.. image:: https://img.shields.io/pypi/pyversions/cssselect.svg
   :target: https://pypi.python.org/pypi/cssselect
   :alt: Supported Python Versions

.. image:: https://github.com/scrapy/cssselect/actions/workflows/tests.yml/badge.svg
   :target: https://github.com/scrapy/cssselect/actions/workflows/tests.yml
   :alt: Tests

.. image:: https://img.shields.io/codecov/c/github/scrapy/cssselect/master.svg
   :target: https://codecov.io/github/scrapy/cssselect?branch=master
   :alt: Coverage report

**cssselect** is a BSD-licensed Python library to parse `CSS3 selectors`_ and
translate them to `XPath 1.0`_ expressions.

`XPath 1.0`_ expressions can be used in lxml_ or another XPath engine to find
the matching elements in an XML or HTML document.

Find the cssselect online documentation at https://cssselect.readthedocs.io.

Quick facts:

* Source, issues and pull requests `on GitHub
  <https://github.com/scrapy/cssselect>`_
* Releases `on PyPI <https://pypi.org/project/cssselect/>`_
* Install with ``pip install cssselect``


.. _CSS3 selectors: https://www.w3.org/TR/selectors-3/
.. _XPath 1.0: https://www.w3.org/TR/xpath/all/
.. _lxml: https://lxml.de/
