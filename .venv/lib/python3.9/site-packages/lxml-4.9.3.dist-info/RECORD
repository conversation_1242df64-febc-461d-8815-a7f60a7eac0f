lxml-4.9.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
lxml-4.9.3.dist-info/METADATA,sha256=hW6-yB5JIhWexx6FcUorgGlXar8zlg28y66hAJG5Ims,4015
lxml-4.9.3.dist-info/RECORD,,
lxml-4.9.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml-4.9.3.dist-info/WHEEL,sha256=VL3jD_4WjvScOwZFECHd378W1ZiFm8-5W7AEtBTx7Cc,107
lxml-4.9.3.dist-info/licenses/LICENSE.txt,sha256=ae20RcEzWoMS1MCScYR-mVbYTw2fck0SU0DMP612eyo,1488
lxml-4.9.3.dist-info/licenses/LICENSES.txt,sha256=QdSd1AaqDhVIptXyGjDWv2OLPNlutyid00jYPtLkA5I,1514
lxml-4.9.3.dist-info/top_level.txt,sha256=NjD988wqaKq512nshNdLt-uDxsjkp4Bh51m6N-dhUrk,5
lxml/ElementInclude.py,sha256=PSLeZFvCa76WHJulPLxcZXJtCI2-4dK2CtqPRiYOAQg,8560
lxml/__init__.py,sha256=71f4Pgl0xksXqReX16a_Q4bf88TgXzKQnhEjGvLaQ10,575
lxml/__pycache__/ElementInclude.cpython-39.pyc,,
lxml/__pycache__/__init__.cpython-39.pyc,,
lxml/__pycache__/_elementpath.cpython-39.pyc,,
lxml/__pycache__/builder.cpython-39.pyc,,
lxml/__pycache__/cssselect.cpython-39.pyc,,
lxml/__pycache__/doctestcompare.cpython-39.pyc,,
lxml/__pycache__/pyclasslookup.cpython-39.pyc,,
lxml/__pycache__/sax.cpython-39.pyc,,
lxml/__pycache__/usedoctest.cpython-39.pyc,,
lxml/_elementpath.cpython-39-darwin.so,sha256=1CKkUWZZUZE-Iu_1S1wxIoc1uTqV_m5UC88tG43yBCE,225424
lxml/_elementpath.py,sha256=wo6_CnGtKSkadI-krW8gbEZ1fVPTnIJINNwrdRfT_fw,10742
lxml/apihelpers.pxi,sha256=X9VHTgQGjf6F3AgG4BTHFqzoUWUclScGjileIvCMtFc,64452
lxml/builder.cpython-39-darwin.so,sha256=6kZEcCRK1_Q9liAcDcOwj4p6q67yvNp7Jzt-tWDn7ic,135680
lxml/builder.py,sha256=_eBVChPcD4XhLe4cXC3USsuV3tzNyHcLLykZnHPCfyg,8147
lxml/classlookup.pxi,sha256=AocnunTzIJiJE87ILWLy3FoiP7AiPoezGk-JJjrCQiM,22462
lxml/cleanup.pxi,sha256=L65WiKrJSOODfqMLVMMR8SFE4_BwjVHeudnPAUZcxNc,8458
lxml/cssselect.py,sha256=-4m3YmKAU-iXEJsnleNRQK4K7tPmAoRdhuikOqbUxBU,3364
lxml/debug.pxi,sha256=GorntYkTOs2Bm_HzI5ck3yPNPZAGzc3L37sKOiOSCOY,3283
lxml/docloader.pxi,sha256=DXwTMv2MR37LfVxtLmg9CFUMbHpuZ5X-AqXMF-sqzik,5783
lxml/doctestcompare.py,sha256=dAjqNzMGJuDsxY0xOXwOWzEsq7gSfQf-6uuxZZwaNXM,18339
lxml/dtd.pxi,sha256=-ytHvi8sDedEX2q9_3i3JbA1FUtSOhUzW3wpKKeUBZs,15219
lxml/etree.cpython-39-darwin.so,sha256=KVDsrk1wByyvGY7oQVw2L2GgDK5aL7Ce1_DCHHtAs1g,2297256
lxml/etree.h,sha256=zH_dnQObaEbqPbLlrSjzkQU_f84P7NuBuCll5Psqads,8575
lxml/etree.pyx,sha256=Pu0C0c2DDHA33CWnPnxRYGx4pi0uF63JtzaCbAIVnNk,132415
lxml/etree_api.h,sha256=tgRCNFScrABi7OMWf9ouVJ33fW0Bd8j6mYMpEMsXqZU,17851
lxml/extensions.pxi,sha256=RGenDcaZ-tJiw_EJKk9BTUSZFhcPU2CQ_c8FHwVO9k0,33241
lxml/html/ElementSoup.py,sha256=s_dLobLMuKn2DhexR-iDXdZrMFg1RjLy1feHsIeZMpw,320
lxml/html/__init__.py,sha256=rbuJ5hfzlh37D9AMd7Srj6D17l9wL6Rub0-IUVGVx04,64937
lxml/html/__pycache__/ElementSoup.cpython-39.pyc,,
lxml/html/__pycache__/__init__.cpython-39.pyc,,
lxml/html/__pycache__/_diffcommand.cpython-39.pyc,,
lxml/html/__pycache__/_html5builder.cpython-39.pyc,,
lxml/html/__pycache__/_setmixin.cpython-39.pyc,,
lxml/html/__pycache__/builder.cpython-39.pyc,,
lxml/html/__pycache__/clean.cpython-39.pyc,,
lxml/html/__pycache__/defs.cpython-39.pyc,,
lxml/html/__pycache__/diff.cpython-39.pyc,,
lxml/html/__pycache__/formfill.cpython-39.pyc,,
lxml/html/__pycache__/html5parser.cpython-39.pyc,,
lxml/html/__pycache__/soupparser.cpython-39.pyc,,
lxml/html/__pycache__/usedoctest.cpython-39.pyc,,
lxml/html/_diffcommand.py,sha256=7-tz3udrgg0unGPAI8pa_uN4e7vW0MmgOXE43kKPdw8,2121
lxml/html/_html5builder.py,sha256=cASxN0Tks3_vqCA_sXa1oCx_McyRL6VpuRLA1T-B58o,3246
lxml/html/_setmixin.py,sha256=uVCgBUC4SJ7N9GotmlKHrhH7R4Kk7wGU3u1WmEJKGeM,1184
lxml/html/builder.py,sha256=aRgS-Ea9bli-muGX0iUQGKAe9D93P8BspQ2WPuiWJcU,4492
lxml/html/clean.cpython-39-darwin.so,sha256=7ZFbphR9Zjq-1fRQt_poObqbYoJ3NEx7HcpKRArgaTM,274376
lxml/html/clean.py,sha256=LjnNRaN2xyc_m7Dd3XrDv9b_Anzv84pBUQQDUDsCK3U,28249
lxml/html/defs.py,sha256=ZzOp2TmY9f_ein9GIcDPyN8-f5HVptzSj56umimWub4,4236
lxml/html/diff.cpython-39-darwin.so,sha256=aDw4F_3-VfH1whcwwfCaQjDLU5xNayjzseWLYl_S_4o,360504
lxml/html/diff.py,sha256=_juYjb3u9ZIZTyC2_Jl0zpzmIJccFQgr9fWTK4YpO3E,30553
lxml/html/formfill.py,sha256=9lnv7BnrQS0HOBY8ToeP1408xMN1wnltpsY-0CTGBpQ,9689
lxml/html/html5parser.py,sha256=dnyC4cqHxywjZSzk0mu2L7THTZjxhg4yF4pncjusa_w,8634
lxml/html/soupparser.py,sha256=tfdraMayPbMBCd2kGxUoTvNkhKUclfP3LmV9R85WKI4,10203
lxml/html/usedoctest.py,sha256=tPlmVz4KK1GRKV5DJLrdVECeqsT9PlDzSqqTodVi5s0,249
lxml/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/__pycache__/__init__.cpython-39.pyc,,
lxml/includes/c14n.pxd,sha256=pGf910mVH9IdBb7r_aE-J59axIQcqFH4Sx_Tm0PA1m0,1123
lxml/includes/config.pxd,sha256=H6Mrl8It21hzRI2hzMId9W48QqkYYkoLT4dniLNmdTw,96
lxml/includes/dtdvalid.pxd,sha256=Rf2vRBbM4O1AOiIsUk_5M7pV3Dz309sS7Ccd2zGFHT0,671
lxml/includes/etree_defs.h,sha256=3EV64z9DyzIZG_21pSkzAI0F0bs_Zd7GQWbLC8H-8lI,15671
lxml/includes/etreepublic.pxd,sha256=3cdjIVlfkeZWYUav4y_T2uHwAo8yUCTlCvNLEvsZ_aI,10122
lxml/includes/htmlparser.pxd,sha256=Va2qbs5zVokERn57HbDY__CiBQOoCS4uI9wEfCnT6zk,2868
lxml/includes/lxml-version.h,sha256=eiP5zGtOCiw2fc3tjHNZR2Z_7TPJWwtziHVwuMTK858,71
lxml/includes/relaxng.pxd,sha256=12yapjqDZLF_HTlcuSXSoQpPGK1NU7fj7gzS1EF8kZw,2669
lxml/includes/schematron.pxd,sha256=5_PUpLHTzzYZ_d-8d2OjKLdwtLIdOm7C20HFUAX8hD4,1640
lxml/includes/tree.pxd,sha256=dtnXNbEfxV3-5kOwWYkYoCE8HT5zvFVNdKFaIBuNXBc,20091
lxml/includes/uri.pxd,sha256=5wPtpGU1JtdmpZMTzR8EswazihP3dxkns6Fgo9NWOt8,139
lxml/includes/xinclude.pxd,sha256=onXD71LVdAbXjUj82_SDtSixNsNh8xbu6Nd9x0V3bmM,852
lxml/includes/xmlerror.pxd,sha256=JDY_9OKoW5nue7wFe1xft0vk1kmAzo6nVC5DmhKXWI0,58004
lxml/includes/xmlparser.pxd,sha256=X6ab9_HX8gKoQVgdLy9tdsFEdYfayihV5dDhQ9LgGmA,10869
lxml/includes/xmlschema.pxd,sha256=yYQFrIKAQ_feenENV24X2AZyBIYGBltRDm9qB7CYMww,1696
lxml/includes/xpath.pxd,sha256=tKYAcwpbSRq8qrsZ2ISVYvEaLnCV9GadNC5o_f8Ua_g,5794
lxml/includes/xslt.pxd,sha256=qBU-0dLhIMQFv58I4q1XkBq9QJpJzKEAK9qBFPXBj_g,8341
lxml/isoschematron/__init__.py,sha256=NUoI0bb87VB2XePzFcMtwUasrUAQACfb3Z9dPaMz6Fs,12399
lxml/isoschematron/__pycache__/__init__.cpython-39.pyc,,
lxml/isoschematron/resources/rng/iso-schematron.rng,sha256=VsWxPyi3iViJDDbjJJw0wWkEHkLrz9zoCA8zJLor9N4,18337
lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl,sha256=ObebsB8Wt-d3uIA_U5NU85TpnQ3PxPX38TdOAqosMac,3172
lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl,sha256=QweRrIIM-zFcgg98GXA2CaWfIbgVE0XKEeYSfvv67A0,4563
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl,sha256=xSZ_Ekq_I-62ZpiE5AqYYHwFW_qh855zt9V4_s7rbkY,11703
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl,sha256=x42QJ-dxQ1waPzydsCoQnp2Xj15y53nW43O7BuoDRHk,39957
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl,sha256=Tr9BnO6pzjVWwhqJfm10UlvAy95EgfSCz2iMlrVGT6Q,2015
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl,sha256=ue8q_88X4e_jsJizo31GRNBxNhdxkEE9fY20oq0Iqwk,71764
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl,sha256=BBAdsVSi5zAzeGepuN6gS1saQINDqITXKplmmj4dTWg,20382
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt,sha256=OGLiFswuLJEW5EPYKOeoauuCJFEtVa6jyzBE1OcJI98,3310
lxml/iterparse.pxi,sha256=uYVCe5hRTf1MP6MdFD4nwvC_LhHXt4CaXkfigJMdHEU,16607
lxml/lxml.etree.h,sha256=zH_dnQObaEbqPbLlrSjzkQU_f84P7NuBuCll5Psqads,8575
lxml/lxml.etree_api.h,sha256=kkRNu4Gut2eFWdRGW2SEm_-1eKZERFGYlNNaHIyQYTc,17856
lxml/nsclasses.pxi,sha256=84Y3KIXCUQNl64e5Cg0Bcqaq2jzCB8Mmndn07oY3osQ,9145
lxml/objectify.cpython-39-darwin.so,sha256=ECu_K4NSjkPM0cVFz3UvEjahrbSnrFK8fD__1sZZmws,449136
lxml/objectify.pyx,sha256=B8qHvDa2VWlU4Z9K3vaCCewjfZpvFNNqKH4E3fuP10Y,77053
lxml/objectpath.pxi,sha256=32MJBiv1lOYCZpom_2oAW27yNauWrG7ym7_5SJEPk9U,11479
lxml/parser.pxi,sha256=Nhjp3bkJcDav2cu4eEW-4SlGnnRQyiEbSh-La43vOx4,78247
lxml/parsertarget.pxi,sha256=THBYdmmRYCoj68BEv-BMXR3_NI8ME9OWVJBdypAhuQY,6859
lxml/proxy.pxi,sha256=Tna5v8R1eJ50mJbbBHHFEUr2ndiQo8eV1pzsYzXhj-c,23562
lxml/public-api.pxi,sha256=LYxBK_CXxI7NLzzSCXQaNqRjpUhWuD1o4fK-W_NxVvQ,6660
lxml/pyclasslookup.py,sha256=gLD1HM2HtITYYiGzjEOewSwbB7XkVx_NZv_quCt79Oc,92
lxml/readonlytree.pxi,sha256=jIvwD-ctSm7WLK5yAPlev_xGddpCouqi-OpinOIP0RM,19048
lxml/relaxng.pxi,sha256=xQWG3HsUEzTQIis9MUKDeOq25jOJJm3X1CgOWkQfEZ4,6085
lxml/sax.cpython-39-darwin.so,sha256=0t87PnhNTKTtqkEO0DPCiMoNhypxd3BkkqjqXwam3p8,196840
lxml/sax.py,sha256=xcs8RDKFWnqCbZpYdDuJPLIUTbThrCYnX9arDhxlgOY,9396
lxml/saxparser.pxi,sha256=0R_0Pi0F9Y_1IWuJ_1AhaMp_I4kkubsovk1bt1RSRYc,32542
lxml/schematron.pxi,sha256=6ZvkG5fDcBLYEdwSaXplwcgbp8eiBexEo6UZ6Tl-sug,5782
lxml/serializer.pxi,sha256=Y0k1Knswc5bNkd7Qv094K5bfD_QyG-IYyWbhi6xRYqY,67999
lxml/usedoctest.py,sha256=qRgZKQVcAZcl-zN0AIXVJnOsETUXz2nPXkxuzs1lGgk,230
lxml/xinclude.pxi,sha256=LPL_U0dx-qLP2slshTaOkIObeI8q35BsTU-eViuNn9A,2460
lxml/xmlerror.pxi,sha256=v0ky0Gp9dnwjoMcP9wZ3ukuyjDzRHi2huwdFC3v9sx4,49531
lxml/xmlid.pxi,sha256=v7fXygmofM83LHmM7aZMELNkqIBvIRLc35gABWICfAE,6064
lxml/xmlschema.pxi,sha256=FmDRpaEJXNmNV57nWnZlK_xKyqZt2rz2OLs3PO4iKhI,8079
lxml/xpath.pxi,sha256=4-Su_FRyrBuj0GUcneqJcuFX8O0cjTlJqrztZBazgoY,19571
lxml/xslt.pxi,sha256=-vxp7JNgRRLsupTDcZt4JIvIp1xpGPYYECv0BbR3BOg,36694
lxml/xsltext.pxi,sha256=UJbJF6ZZrbgbBWDgDMUh5MpbNhEMEZ8kWqea_TLfZzA,11085
