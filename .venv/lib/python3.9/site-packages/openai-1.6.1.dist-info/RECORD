../../../bin/openai,sha256=waeNTnZkYea46_QBoo0f7Yl1zwZShRo5B4pkxL6LVjc,282
openai-1.6.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
openai-1.6.1.dist-info/METADATA,sha256=82R3hQSSsgm7RKxujMSZaJ4QAfziFXEIz1JdevxJKD0,17143
openai-1.6.1.dist-info/RECORD,,
openai-1.6.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai-1.6.1.dist-info/WHEEL,sha256=mRYSEL3Ih6g5a_CVMIcwiF__0Ae4_gLYh01YFNwiq1k,87
openai-1.6.1.dist-info/entry_points.txt,sha256=kAYhQEmziJwsKs5raYAIOvJ2LWmbz5dulEXOzsY71ro,43
openai-1.6.1.dist-info/licenses/LICENSE,sha256=Sa73AcxOcVUuCfzKf62Fu6hK2T7keXrLoEPQPTErZTY,11336
openai/__init__.py,sha256=pgRzO2ACcXX0pnsyeq9CZI8D6cUldHd26Gh_fvNoqIE,9361
openai/__main__.py,sha256=bYt9eEaoRQWdejEHFD8REx9jxVEdZptECFsV7F49Ink,30
openai/__pycache__/__init__.cpython-39.pyc,,
openai/__pycache__/__main__.cpython-39.pyc,,
openai/__pycache__/_base_client.cpython-39.pyc,,
openai/__pycache__/_client.cpython-39.pyc,,
openai/__pycache__/_compat.cpython-39.pyc,,
openai/__pycache__/_constants.cpython-39.pyc,,
openai/__pycache__/_exceptions.cpython-39.pyc,,
openai/__pycache__/_files.cpython-39.pyc,,
openai/__pycache__/_models.cpython-39.pyc,,
openai/__pycache__/_module_client.cpython-39.pyc,,
openai/__pycache__/_qs.cpython-39.pyc,,
openai/__pycache__/_resource.cpython-39.pyc,,
openai/__pycache__/_response.cpython-39.pyc,,
openai/__pycache__/_streaming.cpython-39.pyc,,
openai/__pycache__/_types.cpython-39.pyc,,
openai/__pycache__/_version.cpython-39.pyc,,
openai/__pycache__/pagination.cpython-39.pyc,,
openai/__pycache__/version.cpython-39.pyc,,
openai/_base_client.py,sha256=5I3UkOn8SExBgu7ayxOoG-QaCFus6VmW8d4RdAH6ddY,57627
openai/_client.py,sha256=ejvAzGMcseTBdE5uqCeZBrOWh_G4wQEti5YhyjLvAY0,18698
openai/_compat.py,sha256=pofRsaU2XT43Ij6aMRr3XfKgIVIJfAEUeElVFaE4cTA,5102
openai/_constants.py,sha256=cPUiQ4CPnBodn9VWw5Nz9z-obvNkGqKtSbg1W88Nb1o,382
openai/_exceptions.py,sha256=fD-fu3uXySRumGkHWzTT21ojAiR0d8_Go3Ujs2UdpC8,3600
openai/_extras/__init__.py,sha256=PpHi-jjIVLWp4G1f5AfNQRJnoCxKiYU9CVMAyN-Crwo,131
openai/_extras/__pycache__/__init__.cpython-39.pyc,,
openai/_extras/__pycache__/_common.cpython-39.pyc,,
openai/_extras/__pycache__/numpy_proxy.cpython-39.pyc,,
openai/_extras/__pycache__/pandas_proxy.cpython-39.pyc,,
openai/_extras/_common.py,sha256=NWWtgbdJsO3hQGQxaXGfVk0LjeIE5AFZ8VS_795hhMc,364
openai/_extras/numpy_proxy.py,sha256=nYeA0zUsLPsOYFoK-r3hr0MjG8QMBjyO3tU7a1KW8Js,850
openai/_extras/pandas_proxy.py,sha256=K-GJTaJiyG7Dq3A8-Hn1lCwX7UPFrhDVntI2Aref0Fo,688
openai/_files.py,sha256=Ow1uBpIr0bLnIHxtN-mB4Lsa4uVgNU6wLVOkgqi9Gzc,3470
openai/_models.py,sha256=YquA5bGsV545mDpUNo936tIeyh-cv95ICpJdSUvboUc,16467
openai/_module_client.py,sha256=W-lfWtHAs0dg5XEJfIuLaTtfpkjVfIQb2Ve4VZ3fsc8,2702
openai/_qs.py,sha256=AOkSz4rHtK4YI3ZU_kzea-zpwBUgEY8WniGmTPyEimc,4846
openai/_resource.py,sha256=F_pXU86CNv_FFBPjW7Pwcr4wOkcBaEEkoeQOGrwLlXY,1070
openai/_response.py,sha256=UHYhj4cw8ugVayp-MgU1aok52m3QjuVMJGkA--DpZ6s,9395
openai/_streaming.py,sha256=ImWFF1UGMl7uoe4Ysm1FUP7dvUGVQQubJtoJfO5uaQs,8010
openai/_types.py,sha256=7mvAEAS_Iibo4Re6NktoSYemeXkGlLKJWkVEdbYVDfg,10069
openai/_utils/__init__.py,sha256=gVHDuL3vbVhVvROohD64pfUwiho6SZsZVSesWa5_mSw,2131
openai/_utils/__pycache__/__init__.cpython-39.pyc,,
openai/_utils/__pycache__/_logs.cpython-39.pyc,,
openai/_utils/__pycache__/_proxy.cpython-39.pyc,,
openai/_utils/__pycache__/_streams.cpython-39.pyc,,
openai/_utils/__pycache__/_transform.cpython-39.pyc,,
openai/_utils/__pycache__/_typing.cpython-39.pyc,,
openai/_utils/__pycache__/_utils.cpython-39.pyc,,
openai/_utils/_logs.py,sha256=sFA_NejuNObTGGbfsXC03I38mrT9HjsgAJx4d3GP0ok,774
openai/_utils/_proxy.py,sha256=SyQdO2oTSPGXj2ACEwYl520EMr4hh6hl1fIOuJ9ebes,2302
openai/_utils/_streams.py,sha256=SMC90diFFecpEg_zgDRVbdR3hSEIgVVij4taD-noMLM,289
openai/_utils/_transform.py,sha256=nCmRhC_-gznT5NCo_n7IxtTeTwcR-7TI8iN5DdUt_NI,7016
openai/_utils/_typing.py,sha256=-japjP-CqMESriJzRV5Ly2KGUqPzNcoX4qSv_gRmCRo,2569
openai/_utils/_utils.py,sha256=hX0k8BlvJAqwPwUMJJCswbHVpVYsdoQQD5Mjo5sJang,11026
openai/_version.py,sha256=6z4UkuiZ55q0xP_kr5gvYQWXLW_v1_mPHsAmgmzyG1w,125
openai/cli/__init__.py,sha256=soGgtqyomgddl92H0KJRqHqGuaXIaghq86qkzLuVp7U,31
openai/cli/__pycache__/__init__.cpython-39.pyc,,
openai/cli/__pycache__/_cli.cpython-39.pyc,,
openai/cli/__pycache__/_errors.cpython-39.pyc,,
openai/cli/__pycache__/_models.cpython-39.pyc,,
openai/cli/__pycache__/_progress.cpython-39.pyc,,
openai/cli/__pycache__/_utils.cpython-39.pyc,,
openai/cli/_api/__init__.py,sha256=cj92MZq-9_1PQM8A4TQVsqKn5mcTDAGxHllJ0UvJOPE,58
openai/cli/_api/__pycache__/__init__.cpython-39.pyc,,
openai/cli/_api/__pycache__/_main.cpython-39.pyc,,
openai/cli/_api/__pycache__/audio.cpython-39.pyc,,
openai/cli/_api/__pycache__/completions.cpython-39.pyc,,
openai/cli/_api/__pycache__/files.cpython-39.pyc,,
openai/cli/_api/__pycache__/image.cpython-39.pyc,,
openai/cli/_api/__pycache__/models.cpython-39.pyc,,
openai/cli/_api/_main.py,sha256=5yyfLURqCEaAN8B61gHaqVAaYgtyb9Xq0ncQ3P2BAh0,451
openai/cli/_api/audio.py,sha256=HZDTRZT-qZTMsg7WOm-djCQlf874aSa3lxRvNG27wLM,3347
openai/cli/_api/chat/__init__.py,sha256=MhFUQH9F6QCtbPMlbsU_DWTd7wc5DSCZ7Wy3FBGVij0,300
openai/cli/_api/chat/__pycache__/__init__.cpython-39.pyc,,
openai/cli/_api/chat/__pycache__/completions.cpython-39.pyc,,
openai/cli/_api/chat/completions.py,sha256=9Ztetyz7rm0gP5SOPWEcpzFJnJKuIEQit626vOq42bE,5363
openai/cli/_api/completions.py,sha256=ysOmnbXpFz3VB5N_5USPdObiYew62vEn6rMtNFwTJGQ,6412
openai/cli/_api/files.py,sha256=6nKXFnsC2QE0bGnVUAG7BTLSu6K1_MhPE0ZJACmzgRY,2345
openai/cli/_api/image.py,sha256=VKMRqKCHkl4JO7uP7RLDZu8DzF6ddQgpr3n2v9EOEBk,4711
openai/cli/_api/models.py,sha256=pGmIGZToj3raGGpKvPSq_EVUR-dqg4Vi0PNfZH98D2E,1295
openai/cli/_cli.py,sha256=WxqTnhVVtfzX0z7hV5fcvd3hkihaUgwOWpXOwyCS4Fc,6743
openai/cli/_errors.py,sha256=7BYF2Kp_L6yKsZDNdg-gK71FMVCNjhrunfVVgh4Zy0M,479
openai/cli/_models.py,sha256=tgsldjG216KpwgAZ5pS0sV02FQvONDJU2ElA4kCCiIU,491
openai/cli/_progress.py,sha256=aMLssU9jh-LoqRYH3608jNos7r6vZKnHTRlHxFznzv4,1406
openai/cli/_tools/__init__.py,sha256=cj92MZq-9_1PQM8A4TQVsqKn5mcTDAGxHllJ0UvJOPE,58
openai/cli/_tools/__pycache__/__init__.cpython-39.pyc,,
openai/cli/_tools/__pycache__/_main.cpython-39.pyc,,
openai/cli/_tools/__pycache__/fine_tunes.cpython-39.pyc,,
openai/cli/_tools/__pycache__/migrate.cpython-39.pyc,,
openai/cli/_tools/_main.py,sha256=pakjEXHRHqYlTml-RxV7fNrRtRXzmZBinoPi1AJipFY,467
openai/cli/_tools/fine_tunes.py,sha256=RQgYMzifk6S7Y1I1K6huqco2QxmXa7gVUlHl6SrKTSU,1543
openai/cli/_tools/migrate.py,sha256=PqJgT-7KrCiue5Wj-1YaXfKNqcGFHXnrC9EU4eHX39c,4895
openai/cli/_utils.py,sha256=oiTc9MnxQh_zxAZ1OIHPkoDpCll0NF9ZgkdFHz4T-Bs,848
openai/lib/__pycache__/_old_api.cpython-39.pyc,,
openai/lib/__pycache__/_validators.cpython-39.pyc,,
openai/lib/__pycache__/azure.cpython-39.pyc,,
openai/lib/_old_api.py,sha256=XZnXBrEKuTd70iJirj5mGW35fZoqruJobbBTq6bvg10,1947
openai/lib/_validators.py,sha256=jnVLH1mIN1zumudXyxv1UjyXJpd7FLIU719wiRIBues,35189
openai/lib/azure.py,sha256=5e4SHv3nN0xoIOBWV1hvoOLkhCyaXpdX6fQtPXBMxh8,20924
openai/pagination.py,sha256=YTDaYhbbTowvnTIXW6hsFCegnKi880BMjnfUC3PRRkM,2805
openai/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/resources/__init__.py,sha256=H1yP0Iagv-YamT8ySJ028Dre8nphLwAdwo0OZI2JVT0,2588
openai/resources/__pycache__/__init__.cpython-39.pyc,,
openai/resources/__pycache__/completions.cpython-39.pyc,,
openai/resources/__pycache__/edits.cpython-39.pyc,,
openai/resources/__pycache__/embeddings.cpython-39.pyc,,
openai/resources/__pycache__/files.cpython-39.pyc,,
openai/resources/__pycache__/fine_tunes.cpython-39.pyc,,
openai/resources/__pycache__/images.cpython-39.pyc,,
openai/resources/__pycache__/models.cpython-39.pyc,,
openai/resources/__pycache__/moderations.cpython-39.pyc,,
openai/resources/audio/__init__.py,sha256=2nXrKGjWa_ejR51Q1x192sJb5t75oiMuqjhtGB6xsf4,997
openai/resources/audio/__pycache__/__init__.cpython-39.pyc,,
openai/resources/audio/__pycache__/audio.cpython-39.pyc,,
openai/resources/audio/__pycache__/speech.cpython-39.pyc,,
openai/resources/audio/__pycache__/transcriptions.cpython-39.pyc,,
openai/resources/audio/__pycache__/translations.cpython-39.pyc,,
openai/resources/audio/audio.py,sha256=XAO3wSlSadTFHgiM86ByXw77EbNAJtAHKEPt5bt2ECc,2234
openai/resources/audio/speech.py,sha256=lFUzuu88pqJL5phutiIEVUDXGYuFqF--Jd2R3EUw_wg,6617
openai/resources/audio/transcriptions.py,sha256=2Rzn3FIc4G7s1XXz4bJJ6kIwTYvHXBMUTXHdE3qAXgo,8852
openai/resources/audio/translations.py,sha256=aU1BKJlsP1l3ZbZkS4JXtJqBxrsbtfDSJ5hvWq5uh10,8000
openai/resources/beta/__init__.py,sha256=YJnI8dasxS3WbubsUnX08dWgkgzfUDeiZp85pIp3n0c,699
openai/resources/beta/__pycache__/__init__.cpython-39.pyc,,
openai/resources/beta/__pycache__/beta.cpython-39.pyc,,
openai/resources/beta/assistants/__init__.py,sha256=rHQZg27vg1Wu64WvGnOoOKY5xtkkagOgrlwo0Z0PQGA,491
openai/resources/beta/assistants/__pycache__/__init__.cpython-39.pyc,,
openai/resources/beta/assistants/__pycache__/assistants.cpython-39.pyc,,
openai/resources/beta/assistants/__pycache__/files.cpython-39.pyc,,
openai/resources/beta/assistants/assistants.py,sha256=CrQBy4_vzjTccVcY65TGfc19JDrFMDz-CznH23jQsKc,27512
openai/resources/beta/assistants/files.py,sha256=EBzW-hcfetg0z-M7thy4ixp9AfLdlCU9bBUFdXfh-Mo,16555
openai/resources/beta/beta.py,sha256=KjcRj6mPjkqXgzIWWWHk5L19e2NShfFBvrWMAWcr9vQ,1690
openai/resources/beta/threads/__init__.py,sha256=KnV1SskwD8RtoxvfXQfrui5xwPc3_n5Py0z1JZG8TOA,681
openai/resources/beta/threads/__pycache__/__init__.cpython-39.pyc,,
openai/resources/beta/threads/__pycache__/threads.cpython-39.pyc,,
openai/resources/beta/threads/messages/__init__.py,sha256=GuDSUTY9hCu14Ia9WuNXbXoM293bhFxksHdHANHCdOA,473
openai/resources/beta/threads/messages/__pycache__/__init__.cpython-39.pyc,,
openai/resources/beta/threads/messages/__pycache__/files.cpython-39.pyc,,
openai/resources/beta/threads/messages/__pycache__/messages.cpython-39.pyc,,
openai/resources/beta/threads/messages/files.py,sha256=rVHzquAgO3Hp82jKXgfUAG-Y78hIKGVCFCzksWDcdc8,10106
openai/resources/beta/threads/messages/messages.py,sha256=F63zHArzKcVwWp7aZyqvPxSjbC61Lsq_lFxt1TS1J0Y,19312
openai/resources/beta/threads/runs/__init__.py,sha256=JDJA_Qyv3V1kDQ56HdWd3G92m1yc5otzJf8G-B3sd4w,416
openai/resources/beta/threads/runs/__pycache__/__init__.cpython-39.pyc,,
openai/resources/beta/threads/runs/__pycache__/runs.cpython-39.pyc,,
openai/resources/beta/threads/runs/__pycache__/steps.cpython-39.pyc,,
openai/resources/beta/threads/runs/runs.py,sha256=L5tZ_EXOYnrSdnSlbsIZsYiR1qiehFzGUA-B4pb1CU8,27402
openai/resources/beta/threads/runs/steps.py,sha256=aKC-jINKL1Er5vJR1xrT1heMvM0DPlsT2iu94k7iZzA,10028
openai/resources/beta/threads/threads.py,sha256=F4iKA-dTl3UyMRK3i4KvRZMyMMtft2iM5RzXnFG6Ees,21598
openai/resources/chat/__init__.py,sha256=8zjX-HmXak-XvoPdstIV-uUF49SBHlHzRrbv-WqeqKE,491
openai/resources/chat/__pycache__/__init__.cpython-39.pyc,,
openai/resources/chat/__pycache__/chat.cpython-39.pyc,,
openai/resources/chat/__pycache__/completions.cpython-39.pyc,,
openai/resources/chat/chat.py,sha256=ysGF5N1Gv6RcWmmqflnfN3rqcUqHjml53j_ruO6a7J0,1337
openai/resources/chat/completions.py,sha256=Be_WAOaClIBUoYxi7d7mnvTNMuCFXF0cwYaCdGrcJ5Y,69655
openai/resources/completions.py,sha256=uDMsoXPnzxZeMl1Yldb-t-YiHOGiQGL8uZLwi2pjnHI,58764
openai/resources/edits.py,sha256=pb4DwpU_RD7YEsywNKwY1p5nfpFugZGI4GK1KRowbWo,7801
openai/resources/embeddings.py,sha256=WIAA9wWL632sgy1xSIpookU2eavfLKrUuD8Lt31PHQc,9298
openai/resources/files.py,sha256=vzngjurWRkf6qw7L1KSKmAUqknphnGoc5S4R03wRzms,22656
openai/resources/fine_tunes.py,sha256=tr5P9-SdB01BY36oNoqAZ3L-0u6iK_TEcbUmku8bsOU,37313
openai/resources/fine_tuning/__init__.py,sha256=r31AXYyHoC42T8WfUrM7tGahQcU4T5MXd3SVxwL2UEE,483
openai/resources/fine_tuning/__pycache__/__init__.cpython-39.pyc,,
openai/resources/fine_tuning/__pycache__/fine_tuning.cpython-39.pyc,,
openai/resources/fine_tuning/__pycache__/jobs.cpython-39.pyc,,
openai/resources/fine_tuning/fine_tuning.py,sha256=29_e1ff_qDd5gToWNXQBOhYbWkM0e_Z2yWdJnNbm8Tw,1283
openai/resources/fine_tuning/jobs.py,sha256=NAVpV3uKQ6oREFJQ_8vQhQOZu0EsuHcSQI7viiri4_0,21922
openai/resources/images.py,sha256=Ud_X0_xdyIHmXcCOKQ2X-9Qrz7I_D5F9D7dDlp0NDek,23001
openai/resources/models.py,sha256=0ozhFDvubVScm5FmsqHwTA5NF5XksalXjhBoVGxW1uw,8575
openai/resources/moderations.py,sha256=ZdE9JjsZoch_qULmrRq53qmVUfVeVvljZylfcjBdOkQ,5790
openai/types/__init__.py,sha256=oLVWb-ZMrKAJMU1kprklR0e87b118Wl0eYcgmSG1vME,2175
openai/types/__pycache__/__init__.cpython-39.pyc,,
openai/types/__pycache__/completion.cpython-39.pyc,,
openai/types/__pycache__/completion_choice.cpython-39.pyc,,
openai/types/__pycache__/completion_create_params.cpython-39.pyc,,
openai/types/__pycache__/completion_usage.cpython-39.pyc,,
openai/types/__pycache__/create_embedding_response.cpython-39.pyc,,
openai/types/__pycache__/edit.cpython-39.pyc,,
openai/types/__pycache__/edit_create_params.cpython-39.pyc,,
openai/types/__pycache__/embedding.cpython-39.pyc,,
openai/types/__pycache__/embedding_create_params.cpython-39.pyc,,
openai/types/__pycache__/file_content.cpython-39.pyc,,
openai/types/__pycache__/file_create_params.cpython-39.pyc,,
openai/types/__pycache__/file_deleted.cpython-39.pyc,,
openai/types/__pycache__/file_list_params.cpython-39.pyc,,
openai/types/__pycache__/file_object.cpython-39.pyc,,
openai/types/__pycache__/fine_tune.cpython-39.pyc,,
openai/types/__pycache__/fine_tune_create_params.cpython-39.pyc,,
openai/types/__pycache__/fine_tune_event.cpython-39.pyc,,
openai/types/__pycache__/fine_tune_events_list_response.cpython-39.pyc,,
openai/types/__pycache__/fine_tune_list_events_params.cpython-39.pyc,,
openai/types/__pycache__/image.cpython-39.pyc,,
openai/types/__pycache__/image_create_variation_params.cpython-39.pyc,,
openai/types/__pycache__/image_edit_params.cpython-39.pyc,,
openai/types/__pycache__/image_generate_params.cpython-39.pyc,,
openai/types/__pycache__/images_response.cpython-39.pyc,,
openai/types/__pycache__/model.cpython-39.pyc,,
openai/types/__pycache__/model_deleted.cpython-39.pyc,,
openai/types/__pycache__/moderation.cpython-39.pyc,,
openai/types/__pycache__/moderation_create_params.cpython-39.pyc,,
openai/types/__pycache__/moderation_create_response.cpython-39.pyc,,
openai/types/audio/__init__.py,sha256=1jElLAc9ZMmKAtRI7zZLhEzRzzt90PevVrBwtUePsqs,479
openai/types/audio/__pycache__/__init__.cpython-39.pyc,,
openai/types/audio/__pycache__/speech_create_params.cpython-39.pyc,,
openai/types/audio/__pycache__/transcription.cpython-39.pyc,,
openai/types/audio/__pycache__/transcription_create_params.cpython-39.pyc,,
openai/types/audio/__pycache__/translation.cpython-39.pyc,,
openai/types/audio/__pycache__/translation_create_params.cpython-39.pyc,,
openai/types/audio/speech_create_params.py,sha256=nvRpp2soI3ljAdR_XE5ally2AHYvdBM_fSrapeKd6bQ,1207
openai/types/audio/transcription.py,sha256=bHHqJzRrdk_p23yvenTGjw0QBnjI3ebem1Ji1IJROOc,164
openai/types/audio/transcription_create_params.py,sha256=H-OP-3omPi9zE3qkAkU-GOuctMJyqiRn6mg64Jx9Yqc,1694
openai/types/audio/translation.py,sha256=qOh5RFGsAjX5fid6vFYtdNNO_7QVtAAZWzHwbcNc224,160
openai/types/audio/translation_create_params.py,sha256=xxG6-xycSd8Zzt6yak1fYUa7BbbwOKxdVzWRbOYUE0A,1404
openai/types/beta/__init__.py,sha256=rzhcv6NjaFw_q70XgEOnLzamMfwj07gkrrFUGOb6JSw,800
openai/types/beta/__pycache__/__init__.cpython-39.pyc,,
openai/types/beta/__pycache__/assistant.cpython-39.pyc,,
openai/types/beta/__pycache__/assistant_create_params.cpython-39.pyc,,
openai/types/beta/__pycache__/assistant_deleted.cpython-39.pyc,,
openai/types/beta/__pycache__/assistant_list_params.cpython-39.pyc,,
openai/types/beta/__pycache__/assistant_update_params.cpython-39.pyc,,
openai/types/beta/__pycache__/thread.cpython-39.pyc,,
openai/types/beta/__pycache__/thread_create_and_run_params.cpython-39.pyc,,
openai/types/beta/__pycache__/thread_create_params.cpython-39.pyc,,
openai/types/beta/__pycache__/thread_deleted.cpython-39.pyc,,
openai/types/beta/__pycache__/thread_update_params.cpython-39.pyc,,
openai/types/beta/assistant.py,sha256=184z9uz2704LkY9nWZXh_a_6XbcDyHU8yvhUQfKSc5U,2559
openai/types/beta/assistant_create_params.py,sha256=iBySbs07eMtetqfYlqgekXKsqxaPaK7Jg61RtFTFx-k,2539
openai/types/beta/assistant_deleted.py,sha256=fV1wGdYtmF8irj2WRf6eR24JodG0z1rM_M5bwqSvoV4,268
openai/types/beta/assistant_list_params.py,sha256=gsSRtdvZMiBxaTCvpfXy3LYKzViPl0ysx9a8tDguxf8,1187
openai/types/beta/assistant_update_params.py,sha256=HV1eoBL1ZxPXqCrDGVvtYvygHgdXz6ornbTXfAfJXXM,2656
openai/types/beta/assistants/__init__.py,sha256=nPm_I-yeVKKJRYqWzGrf9OoVDDDsg1c0Qj936gIEKkk,356
openai/types/beta/assistants/__pycache__/__init__.cpython-39.pyc,,
openai/types/beta/assistants/__pycache__/assistant_file.cpython-39.pyc,,
openai/types/beta/assistants/__pycache__/file_create_params.cpython-39.pyc,,
openai/types/beta/assistants/__pycache__/file_delete_response.cpython-39.pyc,,
openai/types/beta/assistants/__pycache__/file_list_params.cpython-39.pyc,,
openai/types/beta/assistants/assistant_file.py,sha256=RLht24Zg-XbpuKZk2aVXFTH0R0_2c8smvfice7Gekxs,554
openai/types/beta/assistants/file_create_params.py,sha256=J09q5nl2ZGJ4y8ShBJ5ur1PS7XGrNOBSBz0ptzX2-LM,484
openai/types/beta/assistants/file_delete_response.py,sha256=unPwyynR71X4Ap0nJ4KENk2JGOv65SVLiavUVcLTVAc,278
openai/types/beta/assistants/file_list_params.py,sha256=J-AQx5Eu1fgMjHOUBl3u1HGEDkANVRrmIUk4x7EXTN8,1177
openai/types/beta/chat/__init__.py,sha256=WirADquEnYGjBTTLlIC-kNtGf6opfip3z-sKplC_k3Y,89
openai/types/beta/chat/__pycache__/__init__.cpython-39.pyc,,
openai/types/beta/thread.py,sha256=Z_hjXLEiSBmhPNNgn5GKBryOvixYj71oPe1b1JuQmkU,799
openai/types/beta/thread_create_and_run_params.py,sha256=_0KdAovrcW_oj-AuOq-WZMqbsN-sTRj-vtotDoUVx_M,3722
openai/types/beta/thread_create_params.py,sha256=U_YyCEhqNkGW_a7_MEmV1udHj5UiLtv3DvIrPxfQeGM,1617
openai/types/beta/thread_deleted.py,sha256=jrDuKYAP-oKp3-5ZvjoPR4Yr9aZ-VhWbl5vijDdtypA,259
openai/types/beta/thread_update_params.py,sha256=ElYwOy-_AWDgKH_LRJddQO9saiWMiEMQuDK4MrKMeLQ,554
openai/types/beta/threads/__init__.py,sha256=hdORnHIhvktMY9Sx5H07D4YsFxqeWKLYDE4jp7mBHI8,1004
openai/types/beta/threads/__pycache__/__init__.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/message_content_image_file.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/message_content_text.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/message_create_params.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/message_list_params.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/message_update_params.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/required_action_function_tool_call.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/run.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/run_create_params.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/run_list_params.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/run_submit_tool_outputs_params.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/run_update_params.cpython-39.pyc,,
openai/types/beta/threads/__pycache__/thread_message.cpython-39.pyc,,
openai/types/beta/threads/message_content_image_file.py,sha256=OT2xpdVLxorivEtN6wvkBlcA3tl0BBHSp80dknEsJcY,489
openai/types/beta/threads/message_content_text.py,sha256=e4eIkUfqJDGBfYPCfDHrpA3YDr6fkEnZJLVQ8KaW5Bo,1575
openai/types/beta/threads/message_create_params.py,sha256=y7Vj9uVZ_61vwPCpyBDOzrQiycIRrBj4Wvk1ZzZ1_B4,1100
openai/types/beta/threads/message_list_params.py,sha256=GL2Hq8PoEFSbmQ4DVtCtgFSqc8VoFs7ftvVWIZcybkA,1183
openai/types/beta/threads/message_update_params.py,sha256=Zy46a0o5rxrde_REpNZPmx4G89BMP5DHk1E5cMuUNVw,596
openai/types/beta/threads/messages/__init__.py,sha256=NXH8z8CPb2Bhr_delxNMpdmzFLxaUnVyz7TX2GdTpbc,206
openai/types/beta/threads/messages/__pycache__/__init__.cpython-39.pyc,,
openai/types/beta/threads/messages/__pycache__/file_list_params.cpython-39.pyc,,
openai/types/beta/threads/messages/__pycache__/message_file.cpython-39.pyc,,
openai/types/beta/threads/messages/file_list_params.py,sha256=L6pM_ZzxD_N2dPTeuBhe7FzkKbrXL1lF0gmGAjP_lN8,1217
openai/types/beta/threads/messages/message_file.py,sha256=ZzehFlMaprtmwq4zLx8frhA6APldpexoulNQ4oMxYGU,695
openai/types/beta/threads/required_action_function_tool_call.py,sha256=yuF4lNm5UEq8sMR96BeSCDD-C3kdQcbcf2A7VbZwVJ0,855
openai/types/beta/threads/run.py,sha256=a3jZZFoQzd6ymGbaNx1-f9Ph1KerPzH_dLnCiGN7OlU,4451
openai/types/beta/threads/run_create_params.py,sha256=X1S0BXjkq5H4zls0WgBXIdK2YowU2WWSvLvLnsFMpUI,2512
openai/types/beta/threads/run_list_params.py,sha256=TMAVh4Ffwzkx4WW1-L6ezfCB7LY-u2mdLP5phAvxd-A,1175
openai/types/beta/threads/run_submit_tool_outputs_params.py,sha256=_Zvt4gUhFDdHHX0VXPBlH0J8Dj2QAS-i5KqfIaeC_QQ,719
openai/types/beta/threads/run_update_params.py,sha256=lTdDYumU09-BzihovwaUWy_7em9l6d9DXiabK-j60wM,588
openai/types/beta/threads/runs/__init__.py,sha256=WI0XUSX8hsPHJ-VhgGUbIeDl1MKvK25J22rUShWM-fs,583
openai/types/beta/threads/runs/__pycache__/__init__.cpython-39.pyc,,
openai/types/beta/threads/runs/__pycache__/code_tool_call.cpython-39.pyc,,
openai/types/beta/threads/runs/__pycache__/function_tool_call.cpython-39.pyc,,
openai/types/beta/threads/runs/__pycache__/message_creation_step_details.cpython-39.pyc,,
openai/types/beta/threads/runs/__pycache__/retrieval_tool_call.cpython-39.pyc,,
openai/types/beta/threads/runs/__pycache__/run_step.cpython-39.pyc,,
openai/types/beta/threads/runs/__pycache__/step_list_params.cpython-39.pyc,,
openai/types/beta/threads/runs/__pycache__/tool_calls_step_details.cpython-39.pyc,,
openai/types/beta/threads/runs/code_tool_call.py,sha256=c_C8t3FkwkYehflYYBWPKKbracIcF48EI4xiYHBHicU,1632
openai/types/beta/threads/runs/function_tool_call.py,sha256=aN0yWzqSbfcbA7ejKgFwJp3BhaeAFe6Z9_6pZ7Z-t-0,880
openai/types/beta/threads/runs/message_creation_step_details.py,sha256=Ujxn44Aj27RS3m14Q7qe8t7e1izhSHceqLSoYJQxXP0,473
openai/types/beta/threads/runs/retrieval_tool_call.py,sha256=X4DgXBggLkbkxNbVy74Cot0TtzWdZv2WKtTXeHawbQY,481
openai/types/beta/threads/runs/run_step.py,sha256=HwNW1wO1aFSDnU1-7XLC2hyccPE0ZD_0b6iL02PNbZM,2808
openai/types/beta/threads/runs/step_list_params.py,sha256=4RjV9cvUVIESZr7CWkIJbw2T-D9dCmW2uiHCkdh7nRg,1217
openai/types/beta/threads/runs/tool_calls_step_details.py,sha256=WiZ3no8GpAFT26kBybsEPHnd9TyufhuIZXLDOew-2Ig,736
openai/types/beta/threads/thread_message.py,sha256=V8-7QgQ-JzcFU_aLKiR0wqhSvRBbGtFY-1QUfnacVJ4,2061
openai/types/chat/__init__.py,sha256=abBeg0s3E2rcTeScAaIN6L1FumeHDb0sUxRQWTW2otE,2503
openai/types/chat/__pycache__/__init__.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_assistant_message_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_chunk.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_content_part_image_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_content_part_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_content_part_text_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_function_call_option_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_function_message_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_message.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_message_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_message_tool_call.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_message_tool_call_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_named_tool_choice_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_role.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_system_message_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_token_logprob.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_tool_choice_option_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_tool_message_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_tool_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_user_message_param.cpython-39.pyc,,
openai/types/chat/__pycache__/completion_create_params.cpython-39.pyc,,
openai/types/chat/chat_completion.py,sha256=rUiQZ8HHCcDUtMDCfKWUNdGJ2aEvJOJEHR6Te_0A5Sc,2274
openai/types/chat/chat_completion_assistant_message_param.py,sha256=urtNJ-k4hzw4ZE3KbDT1wzMiUcjYjc12sUM8K0SyVbg,1597
openai/types/chat/chat_completion_chunk.py,sha256=FpZNnKFQUTub1WsBGWO1pWUuOuaZu16ez6Gb21AmVzg,4048
openai/types/chat/chat_completion_content_part_image_param.py,sha256=kyqp63mKKPtIdepl8kNrJEaoP8mQvLrLBjDiIK7PCPY,764
openai/types/chat/chat_completion_content_part_param.py,sha256=NidYunfVTItCFaIiFvfC0flbsPv6AVxTW5CDbikP-Jk,462
openai/types/chat/chat_completion_content_part_text_param.py,sha256=_TJtxbHQaU6-I5uEoriVI7rseY7KNdHV9ouA6wJH9f4,396
openai/types/chat/chat_completion_function_call_option_param.py,sha256=3FGW-vOFJ41UYqiiQujJn-M4uGt2MLHEVfzfWVLS0_I,332
openai/types/chat/chat_completion_function_message_param.py,sha256=KB4cXHg__FkweLmXTCg1L7crLOSKsn-fcqtRByXomRc,558
openai/types/chat/chat_completion_message.py,sha256=hybo4l5hlnhnokxaMj8U33e6m02vLT2zbclzbTJdpYg,1242
openai/types/chat/chat_completion_message_param.py,sha256=hNVkRJmP29bjIidWBv6KLIaF9360HACjlNPMQ8UacZc,805
openai/types/chat/chat_completion_message_tool_call.py,sha256=2GS49NL6M9bx3yoFTRrZBtA5bGXMV6Vz87HDLzrVPxE,867
openai/types/chat/chat_completion_message_tool_call_param.py,sha256=TPTxAapX7imm3Je3IxLYZCyvmkP4QYH0ad0wsk1fLlg,976
openai/types/chat/chat_completion_named_tool_choice_param.py,sha256=eZ1mFBPOOavrBLXHpLz1KsnIWfVZiZyYlhWzz9VCgmU,536
openai/types/chat/chat_completion_role.py,sha256=na9kKLwNySYF4CDeuJKx8bo1lc20PHxe46oXHLAb8cY,207
openai/types/chat/chat_completion_system_message_param.py,sha256=WORNNRGXGOUL6JsN6JO19PY7dna50e6ktl5IyhnTFU0,605
openai/types/chat/chat_completion_token_logprob.py,sha256=SwgQzDaLKL-pg2wR68TESXQNYGBn3g4zrEPWxJttEyU,1426
openai/types/chat/chat_completion_tool_choice_option_param.py,sha256=h52I05ob9-LzmY9ssGLzsH_Er5-wR0KH85Ud991l1yM,399
openai/types/chat/chat_completion_tool_message_param.py,sha256=zbH8uNKaiETcJSitUBZ7BWtnD11t3NVIUzqpyan0Qkw,520
openai/types/chat/chat_completion_tool_param.py,sha256=ez93XyQd_u0lAy40cO5aiJuboCNtvWyAZA8JhKFVURY,452
openai/types/chat/chat_completion_user_message_param.py,sha256=bg6vL-RWy48X-EB02TENMU6l4wueN5_5oQk5ddvdR5U,751
openai/types/chat/completion_create_params.py,sha256=6deBp5oiukywKR1Rz8GEv7cHjZdKp5DBEsgZfwFqmKc,10379
openai/types/completion.py,sha256=TWZXMOLfKcZL_kKDgyJAiZ34_nFQiB66ts0svtp02so,1139
openai/types/completion_choice.py,sha256=s5LDShJYGgkKnlaIhz23vSIF-4sGho9qRh6d0-Wllss,925
openai/types/completion_create_params.py,sha256=o4klxTJ8HsVSz9UflgTVp68ekJ3XXfg6xQKGXwHm5ZA,7602
openai/types/completion_usage.py,sha256=H1onC3vuVy5NKSUlW8FBfVzl_PfgFyrB_ytro80ZAjM,401
openai/types/create_embedding_response.py,sha256=quvWyAE48qV_pwTyWTBT7OO-t1AF0zZaMUI8n-PPtCI,765
openai/types/edit.py,sha256=77ccmAyQgNEaYbSQTvX-gFgeamaU_g0DHv6E3Jt9tt0,1129
openai/types/edit_create_params.py,sha256=mjV0iQomNFtIQqfadCI6Sggqp7wYQGb7GiQ-yhKNSGs,1454
openai/types/embedding.py,sha256=EPMqrP_OjLMuiTv0XcLTizicilcdgAw16akbdWD7xIc,604
openai/types/embedding_create_params.py,sha256=aN3Y0hmW11Af2boTiWxnT4VikuhTJnHUKlmsTb6RIxg,1612
openai/types/file_content.py,sha256=GuNtqtZAuHFXS0uIJbTpOvzx_RePrE-lnH3FvNw4dJ8,100
openai/types/file_create_params.py,sha256=4F6XpTDTRGXZjusxXbU-82rvxFAUju04IC6PywtDTYU,840
openai/types/file_deleted.py,sha256=cJQWjVK1EiFj3kwV4ndQnF5Mlqv7ZSXNJtSwEWilL-A,244
openai/types/file_list_params.py,sha256=0IpTyqCAMcMl-Q2W1v-4J_xpoCdSmh1zvv_p-X0OmEQ,277
openai/types/file_object.py,sha256=owuX6HgYMquwzCif65pNSUV-5dtYs09wtjk0ONnfvME,1193
openai/types/fine_tune.py,sha256=MYk77Cx_8V-W4nXTXWcK_tj4YnPkGr2OHQ6sR6r7wiU,2758
openai/types/fine_tune_create_params.py,sha256=yyMgO5R3jhvAWyTr4HvkTFTItjdnUHhl52UPavqIyfU,5591
openai/types/fine_tune_event.py,sha256=VDORrpwH-cII7PDOLGtgKjvzEihoTiAmu6PR9EVL8nU,282
openai/types/fine_tune_events_list_response.py,sha256=o71CP6X6TnvTMOWk7S4R3OEBrF08_OEFB3CyvrMBqXY,340
openai/types/fine_tune_list_events_params.py,sha256=aWTdLI8Wq_5yQ_0ulY0vRqltBFM2dMJahYc9XXXwyMg,1636
openai/types/fine_tuning/__init__.py,sha256=wfu8ciqHopBRPUkZ0D_NkP14-1wYFgVrY8pbCI2i84s,431
openai/types/fine_tuning/__pycache__/__init__.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/fine_tuning_job.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/fine_tuning_job_event.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/job_create_params.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/job_list_events_params.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/job_list_params.cpython-39.pyc,,
openai/types/fine_tuning/fine_tuning_job.py,sha256=Ehrz8xvUJRi3HXbDSs-8S56gDPpM05pH9Uzcbq55fxA,3270
openai/types/fine_tuning/fine_tuning_job_event.py,sha256=TRx0UgtvikmYUiRg0vMEu57cNszpin0gsbbrHiPL-jY,341
openai/types/fine_tuning/job_create_params.py,sha256=ly9Js-QGiQoCoVBVhD7oM-uybkjcPK172ozYI2V6fBM,2578
openai/types/fine_tuning/job_list_events_params.py,sha256=tfpo_bsTGW2ZaPzv5eJyr5F38UEUiwE_MJDNttfKLwo,367
openai/types/fine_tuning/job_list_params.py,sha256=Adm5vy-_0qoKP9Ubf_DOoT_pRkyB1YBdbvbAMAP40hw,363
openai/types/image.py,sha256=HXUIin-8Jb643tEtHYg9YAE59-kqXdycB1p4XqS5qAY,574
openai/types/image_create_variation_params.py,sha256=e1zOZaTKg37Qe6LgDt0haqHPdOpqj4KYmzGfS-dVX_0,1342
openai/types/image_edit_params.py,sha256=0y23-ZDZyS0a_a5E2kfVcjH_BJo5XSi0XAnKxwCCQUU,1702
openai/types/image_generate_params.py,sha256=0XlKCfUmXUi8pQKetb_pri-1qV5xRpU6cjmGynmCFJA,2008
openai/types/images_response.py,sha256=3eOiPIDxOSkxEaNrb0HOnHZmPtFYWQtY_xpC-yJN6U0,241
openai/types/model.py,sha256=d_h7gH9_7A2GrRHw61x8RFPEXM3uOlYqh6XVHGuMQ98,499
openai/types/model_deleted.py,sha256=vDZMiixtF903ce9bGixXpus5J1KrHYGEyu_Eb6kO-m8,195
openai/types/moderation.py,sha256=ik7ueNMlWVKF6tIp0ZerdSSx-r8AZteFSExwzUl3IsY,3947
openai/types/moderation_create_params.py,sha256=VbmYPy75IFhWNeZGV_ocuM_m5w2XdL6ogIDLf0gAwos,921
openai/types/moderation_create_response.py,sha256=S2IXOkUwnlEWH8hKpMI8nPpg2NEiLYE3OfTXL892xvA,451
openai/types/shared/__init__.py,sha256=ZElh_qWWutVp2d2fkFakb8sGeqcB6M_GWtC3SYohO8g,202
openai/types/shared/__pycache__/__init__.cpython-39.pyc,,
openai/types/shared/__pycache__/function_definition.cpython-39.pyc,,
openai/types/shared/__pycache__/function_parameters.cpython-39.pyc,,
openai/types/shared/function_definition.py,sha256=lmbpvpu7Et9FoLWE14pxAr4Sy47moXwQzgPZvX2xuJM,1034
openai/types/shared/function_parameters.py,sha256=Xj6wM1hq1Kp6ZsYyNdbjfW9LLfZwJFH7HWuzqLnMu-0,152
openai/types/shared_params/__init__.py,sha256=ZElh_qWWutVp2d2fkFakb8sGeqcB6M_GWtC3SYohO8g,202
openai/types/shared_params/__pycache__/__init__.cpython-39.pyc,,
openai/types/shared_params/__pycache__/function_definition.cpython-39.pyc,,
openai/types/shared_params/__pycache__/function_parameters.cpython-39.pyc,,
openai/types/shared_params/function_definition.py,sha256=-jNW5TaabjauAoVB2Ov5hzVvlsg1fZ7oerhlwqjFarI,1045
openai/types/shared_params/function_parameters.py,sha256=_zsz301xdUydlEuHd3igO6B660FXZb1muELvc0Eq_a8,188
openai/version.py,sha256=cjbXKO8Ut3aiv4YlQnugff7AdC48MpSndcx96q88Yb8,62
