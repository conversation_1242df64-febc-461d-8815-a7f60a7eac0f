../../../bin/pdfplumber,sha256=URd4DGwZBBvfDtugg7jn4rSlFyQ9eC3crAzJOy7E02s,286
pdfplumber-0.10.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pdfplumber-0.10.3.dist-info/LICENSE.txt,sha256=DUiZTaTRMh45W15kpyTtTexj-5a4isW4j8AUdx5pCCE,1086
pdfplumber-0.10.3.dist-info/METADATA,sha256=ELmCsLMt7U5UO0D0k45m49Gt-aD5X52Dro5RRjmxT5I,38481
pdfplumber-0.10.3.dist-info/RECORD,,
pdfplumber-0.10.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pdfplumber-0.10.3.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
pdfplumber-0.10.3.dist-info/entry_points.txt,sha256=J8kRAszfkV2WC1zbm9qmZL0fJoA5Otuj0CTK8KBtMRU,51
pdfplumber-0.10.3.dist-info/pbr.json,sha256=0D7L2IYWKa0eVZmHoc9XB0tm9vmZDjUkipWYXgK48W8,47
pdfplumber-0.10.3.dist-info/top_level.txt,sha256=YjIN7aBKMOYxyjm5CK6Zf1Lg9uivzLgeHzh1ohrDvKY,11
pdfplumber/__init__.py,sha256=3-5TfR2k3ptvXav1vuhNUnjXT-x4fva91hec0rNJgXw,267
pdfplumber/__pycache__/__init__.cpython-39.pyc,,
pdfplumber/__pycache__/_typing.cpython-39.pyc,,
pdfplumber/__pycache__/_version.cpython-39.pyc,,
pdfplumber/__pycache__/cli.cpython-39.pyc,,
pdfplumber/__pycache__/container.cpython-39.pyc,,
pdfplumber/__pycache__/convert.cpython-39.pyc,,
pdfplumber/__pycache__/ctm.cpython-39.pyc,,
pdfplumber/__pycache__/display.cpython-39.pyc,,
pdfplumber/__pycache__/page.cpython-39.pyc,,
pdfplumber/__pycache__/pdf.cpython-39.pyc,,
pdfplumber/__pycache__/repair.cpython-39.pyc,,
pdfplumber/__pycache__/table.cpython-39.pyc,,
pdfplumber/_typing.py,sha256=cqdfIkE0FA8ibKLyqe0wwiHzIv_fUJHEJxZz4SdZKJ8,263
pdfplumber/_version.py,sha256=G5SxwdUFb8tZQFcpmH9e9roSKcI_67Zg5zks85fizJs,73
pdfplumber/cli.py,sha256=BlNggEqZNXTXnViD2Awg3Q_Py6Mw48IPK75XU8zzR0c,2150
pdfplumber/container.py,sha256=tWpVjwQwK0yRz1h1_XCJQXds2WLJIFmNYW9a-8Sk-cg,5496
pdfplumber/convert.py,sha256=GMshSS09HEMjg0XZI9DstlvODaEKHFPPZbHT__69rMY,3501
pdfplumber/ctm.py,sha256=78fwAWkKHD6tR8_ESGsbuefO-MPKvLGwBtQLyJJxFb4,816
pdfplumber/display.py,sha256=RKffOjCPQ8cJLYiHa16JEcdS9hVg-7XAHUVkd-AEcuw,12127
pdfplumber/page.py,sha256=4VjDiU5c9R7UNsLakh7tiSCbU-eHpTjPpVf6PQk1L3Q,21656
pdfplumber/pdf.py,sha256=1j6f4ablLZFluAqsyrVvFyzANMp05Gr_hcVZXIRX57g,5564
pdfplumber/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pdfplumber/repair.py,sha256=7VoMx24tTdHtGkRYxDdJcxyurBJ7JHU0Abu2iQZSsio,1822
pdfplumber/table.py,sha256=kXRQwv70OldS2gyhgZPGxKgppGInxDOpxwaynzEDDHs,23425
pdfplumber/utils/__init__.py,sha256=dSoIKlP6zm-6MqQMmyuMc4P_2ViB2KdJ3sIblCDebQg,930
pdfplumber/utils/__pycache__/__init__.cpython-39.pyc,,
pdfplumber/utils/__pycache__/clustering.cpython-39.pyc,,
pdfplumber/utils/__pycache__/generic.cpython-39.pyc,,
pdfplumber/utils/__pycache__/geometry.cpython-39.pyc,,
pdfplumber/utils/__pycache__/pdfinternals.cpython-39.pyc,,
pdfplumber/utils/__pycache__/text.cpython-39.pyc,,
pdfplumber/utils/clustering.py,sha256=OFoHVBsuzT3VTl760LTX4oEwWfYfqDcrRkwBg2fpb64,1754
pdfplumber/utils/generic.py,sha256=G8tGTpVJfKptmqwoChMFdPIDt5SpHdkh15Cxy5QILA4,636
pdfplumber/utils/geometry.py,sha256=Sb9CIoadUQYWCxtQUh_6xUHUQ79PpBOphQHpmD-7PUc,8391
pdfplumber/utils/pdfinternals.py,sha256=m8Sxhm5xU1y1LuFVegkjUrRb8m5ktF-VUODF005mPXA,2218
pdfplumber/utils/text.py,sha256=Xjz9Jfe85njAIYOnLiOv2lPwrsKSPW88y9W4TggP7pk,20410
