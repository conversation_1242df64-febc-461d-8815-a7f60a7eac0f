# Power Plant Data Extraction Pipeline

A comprehensive web-based data retrieval pipeline for extracting structured organizational information about power plants using SERP API, Scraper API, and Groq LLM.

## 🎯 Objective

Extract and populate 9 key organizational fields for power plants:

- `cfpp_type`: Power plant classification/type
- `country_name`: Country location
- `currency_in`: Financial reporting currency
- `financial_year`: Accounting period format
- `organization_name`: Owner/operator company name
- `plants_count`: Total facilities owned by organization
- `plant_types`: Technologies operated by organization
- `ppa_flag`: Power Purchase Agreement existence
- `province`: Sub-national region/state

## 🏗️ Architecture

### Phase-by-Phase Implementation

**Phase 1: Project Setup** ✅
- Dependencies and environment configuration
- Data models and validation schemas
- Configuration management

**Phase 2: Search Discovery** ✅
- SERP API integration for multi-stage search
- Query template system for comprehensive coverage
- Source type identification and prioritization

**Phase 3: Content Extraction** ✅
- Scraper API integration for web content extraction
- Content preprocessing and cleaning
- Relevance scoring and filtering

**Phase 4: LLM Processing** 🚧
- Groq Llama 3.3 70b integration (next phase)
- Field-specific extraction prompts
- Confidence scoring and validation

**Phase 5: Data Validation** 🚧
- Cross-source validation
- Fallback strategies for missing data
- Quality assurance and error handling

## 🚀 Quick Start

### 1. Setup

```bash
# Clone and setup
python setup.py

# Edit .env file with your API keys
cp .env.example .env
# Add your API keys to .env
```

### 2. Required API Keys

- **SERP API**: Get from [serpapi.com](https://serpapi.com)
- **Scraper API**: Get from [scraperapi.com](https://scraperapi.com)
- **Groq API**: Get from [groq.com](https://groq.com)

### 3. Test the Pipeline

```bash
# Run component tests
python test_pipeline.py

# Run main pipeline
python -m src.pipeline
```

## 📁 Project Structure

```
├── src/
│   ├── __init__.py
│   ├── models.py          # Data models and schemas
│   ├── config.py          # Configuration management
│   ├── serp_client.py     # SERP API integration
│   ├── scraper_client.py  # Web scraping client
│   └── pipeline.py        # Main orchestrator
├── org_details.json       # Output schema
├── requirements.txt       # Dependencies
├── .env.example          # Environment template
├── setup.py              # Setup script
├── test_pipeline.py      # Test suite
└── README.md
```

## 🔧 Configuration

### Search Strategy

The pipeline uses a multi-stage search approach:

1. **Basic Discovery**: Find general information about the plant
2. **Organizational**: Identify owner/operator details
3. **Technical Details**: Extract plant type and specifications
4. **Location Details**: Determine geographic information
5. **PPA Details**: Check for power purchase agreements
6. **Portfolio Details**: Find organization's other facilities

### Source Prioritization

Sources are ranked by reliability:

1. **Company Official** (Priority 10): Corporate websites, investor relations
2. **Regulatory Filing** (Priority 9): SEC filings, energy commission reports
3. **Government Database** (Priority 8): EIA, energy authorities
4. **Industry Report** (Priority 7): Platts, trade publications
5. **News Article** (Priority 6): Reuters, Bloomberg
6. **Wikipedia** (Priority 5): General encyclopedias

### Content Processing

- **Preprocessing**: HTML cleaning, text extraction, relevance scoring
- **Filtering**: Minimum content length, maximum size limits
- **Deduplication**: Remove duplicate URLs across search categories

## 📊 Current Implementation Status

### ✅ Completed (Phase 1-3)

- [x] Project structure and dependencies
- [x] Data models with Pydantic validation
- [x] Configuration management system
- [x] SERP API client with multi-stage search
- [x] Web scraper with content preprocessing
- [x] Pipeline orchestration framework
- [x] Test suite and setup scripts

### 🚧 In Progress (Phase 4-5)

- [ ] Groq LLM integration for data extraction
- [ ] Field-specific extraction prompts
- [ ] Cross-source validation logic
- [ ] Confidence scoring system
- [ ] Error handling and fallback strategies

### 📋 Next Steps

1. **Implement Groq LLM Integration**
   - Add Groq client for text processing
   - Implement field-specific extraction prompts
   - Add confidence scoring for extracted data

2. **Add Data Validation**
   - Cross-reference data across multiple sources
   - Implement fallback strategies for missing fields
   - Add data quality checks and anomaly detection

3. **Enhance Content Processing**
   - Add PDF processing capabilities
   - Implement RAG for document analysis
   - Optimize content cleaning and preprocessing

4. **Performance Optimization**
   - Add caching for search results
   - Implement parallel processing
   - Optimize API usage and rate limiting

## 🧪 Testing

### Component Tests
```bash
python test_pipeline.py
```

Tests individual components:
- Model validation
- Configuration loading
- API client initialization
- Search query generation

### Integration Tests

Test with real power plants:
- Vogtle Nuclear Power Plant
- Palo Verde Nuclear Generating Station
- Hoover Dam

## 📈 Performance Metrics

- **Search Coverage**: 5 search categories, 10+ queries per plant
- **Content Volume**: Up to 50KB per scraped page
- **Source Diversity**: 6 source types with priority weighting
- **Rate Limiting**: 1-2 second delays between API calls

## 🔍 Example Usage

```python
from src.pipeline import PowerPlantDataPipeline

# Initialize pipeline
pipeline = PowerPlantDataPipeline()

# Extract data
org_details = await pipeline.extract_organizational_details("Vogtle Nuclear Power Plant")

# Save results
await pipeline.save_results(org_details, "vogtle_results.json")
```

## 📝 Notes

- Currently implements search and scraping phases
- LLM extraction is placeholder (returns "unknown" values)
- Designed for extensibility and modular development
- Comprehensive logging for debugging and monitoring

## 🤝 Contributing

This is a phase-by-phase implementation. Current focus is on:
1. Completing LLM integration (Phase 4)
2. Adding data validation (Phase 5)
3. Performance optimization and testing
