"""
Demo script for the power plant data extraction pipeline with Groq LLM integration.
"""
import asyncio
import logging
import os
import json
from datetime import datetime
from src.pipeline import PowerPlantDataPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pipeline_demo.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


async def demo_single_plant(plant_name: str):
    """Demo extraction for a single power plant."""
    print(f"\n{'='*60}")
    print(f"EXTRACTING DATA FOR: {plant_name}")
    print(f"{'='*60}")

    start_time = datetime.now()

    try:
        # Initialize pipeline
        pipeline = PowerPlantDataPipeline()

        # Extract organizational details
        print("\n🔍 Starting data extraction pipeline...")
        org_details = await pipeline.extract_organizational_details(plant_name)

        # Save results
        filename = f"demo_results_{plant_name.lower().replace(' ', '_').replace('-', '_')}.json"
        await pipeline.save_results(org_details, filename)

        # Calculate execution time
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        # Display results
        print(f"\n✅ Extraction completed in {execution_time:.1f} seconds")
        print(f"📁 Results saved to: {filename}")

        print(f"\n📊 EXTRACTED ORGANIZATIONAL DETAILS:")
        print(f"{'='*50}")

        result_dict = org_details.model_dump()
        for field, value in result_dict.items():
            if value not in [None, "", []]:
                if isinstance(value, list):
                    value_str = ", ".join(str(v) for v in value) if value else "[]"
                else:
                    value_str = str(value)
                print(f"  {field.replace('_', ' ').title()}: {value_str}")
            else:
                print(f"  {field.replace('_', ' ').title()}: Not found")

        # Calculate completion percentage
        total_fields = len(result_dict)
        completed_fields = sum(1 for v in result_dict.values() if v not in [None, "", []])
        completion_pct = (completed_fields / total_fields) * 100

        print(f"\n📈 Data Completion: {completed_fields}/{total_fields} fields ({completion_pct:.1f}%)")

        return org_details, execution_time, completion_pct

    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")
        logger.error(f"Demo failed for {plant_name}: {e}")
        return None, 0, 0


async def demo_multiple_plants():
    """Demo extraction for multiple power plants."""
    print("="*80)
    print("POWER PLANT DATA EXTRACTION PIPELINE DEMO")
    print("Phase 4: LLM Integration with Groq Llama 3.3 70b")
    print("="*80)

    # Check API keys
    # required_keys = ['SERP_API_KEY', 'SCRAPER_API_KEY', 'GROQ_API_KEY']
    required_keys = ['b4aedbdc7dc14b48930567434b5f3f2d', 'b4aedbdc7dc14b48930567434b5f3f2d', '********************************************************']
    missing_keys = [key for key in required_keys if not os.getenv(key)]

    if missing_keys:
        print(f"\n❌ Missing API keys: {', '.join(missing_keys)}")
        print("Please configure all required API keys in your .env file")
        return

    print("\n✅ All API keys configured")

    # Test plants with different characteristics
    test_plants = [
        "Vogtle Nuclear Power Plant",  # Nuclear, US
        "Hornsea Wind Farm",          # Wind, UK
        "Noor Solar Complex"          # Solar, Morocco
    ]

    results_summary = []
    total_start_time = datetime.now()

    for i, plant_name in enumerate(test_plants, 1):
        print(f"\n{'='*20} TEST {i}/{len(test_plants)} {'='*20}")

        result, exec_time, completion = await demo_single_plant(plant_name)

        results_summary.append({
            "plant_name": plant_name,
            "success": result is not None,
            "execution_time": exec_time,
            "completion_percentage": completion
        })

        # Brief pause between extractions
        if i < len(test_plants):
            print("\n⏳ Pausing before next extraction...")
            await asyncio.sleep(3)

    # Overall summary
    total_time = (datetime.now() - total_start_time).total_seconds()
    successful_extractions = sum(1 for r in results_summary if r["success"])
    avg_completion = sum(r["completion_percentage"] for r in results_summary if r["success"]) / max(successful_extractions, 1)

    print(f"\n{'='*80}")
    print("DEMO SUMMARY")
    print(f"{'='*80}")
    print(f"Total execution time: {total_time:.1f} seconds")
    print(f"Successful extractions: {successful_extractions}/{len(test_plants)}")
    print(f"Average data completion: {avg_completion:.1f}%")

    print(f"\n📋 DETAILED RESULTS:")
    for result in results_summary:
        status = "✅ Success" if result["success"] else "❌ Failed"
        print(f"  {result['plant_name']}: {status} ({result['completion_percentage']:.1f}% complete, {result['execution_time']:.1f}s)")

    # Save summary
    summary_file = f"demo_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(summary_file, 'w') as f:
        json.dump({
            "demo_timestamp": datetime.now().isoformat(),
            "total_execution_time": total_time,
            "results": results_summary
        }, f, indent=2)

    print(f"\n📁 Demo summary saved to: {summary_file}")


async def demo_field_extraction():
    """Demo individual field extraction capabilities."""
    print(f"\n{'='*60}")
    print("FIELD EXTRACTION CAPABILITIES DEMO")
    print(f"{'='*60}")

    # Sample content for testing
    sample_content = """
    The Vogtle Nuclear Power Plant is a nuclear power plant located in Georgia, United States.
    It is owned and operated by Georgia Power Company, a subsidiary of Southern Company.
    The plant consists of four nuclear reactors with a total capacity of approximately 4,500 MW.
    Georgia Power operates multiple power generation facilities including coal, natural gas,
    nuclear, and renewable energy sources across the southeastern United States.
    The company has a long-term power purchase agreement with the state of Georgia.
    Financial reporting is done in US dollars with a fiscal year ending December 31.
    """

    try:
        from src.groq_client import GroqExtractionClient
        from src.models import ScrapedContent

        # Create mock scraped content
        mock_content = ScrapedContent(
            url="https://example.com/vogtle",
            title="Vogtle Nuclear Power Plant Information",
            content=sample_content,
            content_length=len(sample_content),
            source_type="company_official",
            extraction_timestamp=datetime.now().isoformat(),
            relevance_score=0.9
        )

        # Initialize Groq client
        groq_key = os.getenv('GROQ_API_KEY')
        if not groq_key:
            print("❌ GROQ_API_KEY not configured")
            return

        groq_client = GroqExtractionClient(groq_key)

        # Test individual field extractions
        fields_to_test = [
            "organization_name",
            "cfpp_type",
            "country_name",
            "ppa_flag"
        ]

        print("\n🧪 Testing individual field extractions:")

        for field in fields_to_test:
            try:
                result = await groq_client.extract_field(
                    field, sample_content, "Vogtle Nuclear Power Plant"
                )

                print(f"  {field}: {result.extracted_value} (confidence: {result.confidence_score:.2f})")

            except Exception as e:
                print(f"  {field}: ❌ Failed - {e}")

        print("\n✅ Field extraction demo completed")

    except Exception as e:
        print(f"❌ Field extraction demo failed: {e}")


async def main():
    """Main demo function."""
    try:
        # Run field extraction demo first
        await demo_field_extraction()

        # Run full pipeline demo
        await demo_multiple_plants()

        print(f"\n{'='*80}")
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("Check the generated JSON files for detailed extraction results.")
        print(f"{'='*80}")

    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
