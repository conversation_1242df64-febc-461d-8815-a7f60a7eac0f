"""
Setup script for the power plant data extraction pipeline.
"""
import os
import subprocess
import sys


def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_environment():
    """Setup environment file."""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("Creating .env file from .env.example...")
            with open('.env.example', 'r') as src, open('.env', 'w') as dst:
                dst.write(src.read())
            print("✅ .env file created")
            print("⚠️  Please edit .env file and add your API keys")
        else:
            print("❌ .env.example not found")
            return False
    else:
        print("✅ .env file already exists")
    
    return True


def create_directories():
    """Create necessary directories."""
    directories = ['logs', 'data', 'temp']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"✅ Directory already exists: {directory}")


def main():
    """Main setup function."""
    print("="*50)
    print("POWER PLANT DATA PIPELINE SETUP")
    print("="*50)
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Setup environment
    if not setup_environment():
        return False
    
    # Create directories
    create_directories()
    
    print("\n" + "="*50)
    print("SETUP COMPLETED SUCCESSFULLY!")
    print("="*50)
    print("\nNext steps:")
    print("1. Edit .env file and add your API keys:")
    print("   - SERP_API_KEY (from serpapi.com)")
    print("   - SCRAPER_API_KEY (from scraperapi.com)")
    print("   - GROQ_API_KEY (from groq.com)")
    print("\n2. Run the test script:")
    print("   python test_pipeline.py")
    print("\n3. Run the main pipeline:")
    print("   python -m src.pipeline")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
