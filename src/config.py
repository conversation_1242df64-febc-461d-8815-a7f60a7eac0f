"""
Configuration management for the power plant data retrieval pipeline.
"""
import os
from typing import Dict, List
from dotenv import load_dotenv
from src.models import PipelineConfig

# Load environment variables
load_dotenv()


class Config:
    """Central configuration class."""
    
    def __init__(self):
        self.pipeline = PipelineConfig(
            max_search_results=int(os.getenv('MAX_SEARCH_RESULTS', 10)),
            max_scrape_pages=int(os.getenv('MAX_SCRAPE_PAGES', 5)),
            request_timeout=int(os.getenv('REQUEST_TIMEOUT', 30)),
            retry_attempts=int(os.getenv('RETRY_ATTEMPTS', 3)),
            min_content_length=int(os.getenv('MIN_CONTENT_LENGTH', 100)),
            max_content_length=int(os.getenv('MAX_CONTENT_LENGTH', 50000)),
            confidence_threshold=float(os.getenv('CONFIDENCE_THRESHOLD', 0.7)),
            serp_api_key=os.getenv('SERP_API_KEY', ''),
            scraper_api_key=os.getenv('SCRAPER_API_KEY', ''),
            groq_api_key=os.getenv('GROQ_API_KEY', '')
        )
    
    @property
    def search_query_templates(self) -> Dict[str, List[str]]:
        """Search query templates for different extraction phases."""
        return {
            "basic_discovery": [
                "{plant_name} power plant",
                "{plant_name} power station",
                "{plant_name} generating facility",
                "{plant_name} energy plant"
            ],
            "organizational": [
                "{plant_name} owner operator",
                "{plant_name} power company",
                "{plant_name} utility company",
                "{organization_name} power plants"  # Used when org found
            ],
            "technical_details": [
                "{plant_name} capacity MW specifications",
                "{plant_name} power plant type technology",
                "{plant_name} coal gas nuclear solar wind",
                "{plant_name} generation capacity"
            ],
            "location_details": [
                "{plant_name} location country province",
                "{plant_name} address location",
                "{plant_name} {country} {province}"  # Used when partially known
            ],
            "ppa_details": [
                "{plant_name} PPA power purchase agreement",
                "{plant_name} offtake agreement",
                "{plant_name} long term contract",
                "{plant_name} electricity sales agreement"
            ],
            "portfolio_details": [
                "{organization_name} power plants portfolio",
                "{organization_name} generating assets",
                "{organization_name} energy facilities",
                "{organization_name} power generation capacity"
            ]
        }
    
    @property
    def source_type_indicators(self) -> Dict[str, List[str]]:
        """URL patterns to identify source types."""
        return {
            "company_official": [
                "investor", "about", "corporate", "company",
                "annual-report", "sustainability", "operations"
            ],
            "regulatory_filing": [
                "sec.gov", "edgar", "10-k", "10-q", "8-k",
                "energy-commission", "utility-commission"
            ],
            "government_database": [
                ".gov", "eia.gov", "energy.gov", "epa.gov",
                "iea.org", "irena.org"
            ],
            "industry_report": [
                "platts", "woodmac", "globaldata", "bnef",
                "power-eng", "utility-dive", "energy-central"
            ],
            "news_article": [
                "reuters", "bloomberg", "wsj", "ft.com",
                "power-technology", "renewableenergyworld"
            ],
            "wikipedia": [
                "wikipedia.org", "wikimedia"
            ]
        }
    
    @property
    def url_priority_weights(self) -> Dict[str, int]:
        """Priority weights for different source types."""
        return {
            "company_official": 10,
            "regulatory_filing": 9,
            "government_database": 8,
            "industry_report": 7,
            "news_article": 6,
            "wikipedia": 5,
            "other": 3
        }
    
    @property
    def content_relevance_keywords(self) -> Dict[str, List[str]]:
        """Keywords for content relevance scoring."""
        return {
            "high_relevance": [
                "power plant", "generating station", "power station",
                "MW", "megawatt", "capacity", "generation",
                "electricity", "energy", "utility", "grid"
            ],
            "medium_relevance": [
                "coal", "gas", "nuclear", "solar", "wind", "hydro",
                "biomass", "geothermal", "renewable", "fossil"
            ],
            "organizational": [
                "owner", "operator", "subsidiary", "parent company",
                "acquired", "merger", "joint venture", "partnership"
            ],
            "financial": [
                "annual report", "financial year", "fiscal year",
                "revenue", "earnings", "investment", "funding"
            ],
            "location": [
                "located", "situated", "based", "province", "state",
                "region", "country", "city", "municipality"
            ]
        }
    
    @property
    def extraction_prompts(self) -> Dict[str, str]:
        """LLM prompts for field extraction."""
        return {
            "cfpp_type": """
From the following content about {plant_name}, extract the power plant type/technology.
Look for specific mentions of: coal, gas, natural gas, nuclear, solar, wind, hydro, hydroelectric, biomass, geothermal, oil, combined cycle, cogeneration.
If multiple types are mentioned, return the primary/main type.

Content: {content}

Return only the plant type as a single word (e.g., "coal", "gas", "nuclear", "solar", "wind", "hydro").
If unclear or not found, return "unknown".
""",
            
            "organization_name": """
From the following content about {plant_name}, identify the official company name that owns or operates this power plant.
Look for phrases like: "owned by", "operated by", "subsidiary of", "part of", company names in titles.
Return the full official organization name, not abbreviations.

Content: {content}

Return only the complete official organization name.
If multiple companies are mentioned, return the primary owner/operator.
If unclear or not found, return "unknown".
""",
            
            "country_name": """
From the following content about {plant_name}, identify the country where this power plant is located.
Look for explicit country mentions, addresses, or geographic references.
Return the full official country name (e.g., "United States", not "USA").

Content: {content}

Return only the full country name.
If unclear or not found, return "unknown".
""",
            
            "province": """
From the following content about {plant_name}, identify the state, province, or sub-national region where this power plant is located.
Look for state names, province names, regional identifiers in addresses or location descriptions.

Content: {content}

Return only the state/province/region name.
If unclear or not found, return "unknown".
""",
            
            "plants_count": """
From the following content about {organization_name}, determine the total number of power plants or generating facilities owned/operated by this organization.
Look for phrases like: "operates X plants", "portfolio of X facilities", "X generating stations".
Count only power generation facilities, not other types of facilities.

Content: {content}

Return only a number (integer).
If unclear or not found, return "unknown".
""",
            
            "plant_types": """
From the following content about {organization_name}, identify all types of power generation technologies operated by this organization.
Look for mentions of: coal, gas, nuclear, solar, wind, hydro, biomass, geothermal, oil, etc.
Return as a list of technologies.

Content: {content}

Return as a comma-separated list (e.g., "coal,gas,solar").
If unclear or not found, return "unknown".
""",
            
            "ppa_flag": """
From the following content about {plant_name}, determine if this power plant has a Power Purchase Agreement (PPA) or similar long-term electricity sales contract.
Look for: "PPA", "power purchase agreement", "offtake agreement", "long-term contract", "electricity sales agreement".

Content: {content}

Return "true" if PPA exists, "false" if explicitly no PPA, "unknown" if unclear.
""",
            
            "currency_in": """
From the following content about {organization_name}, identify the primary currency used in financial reporting.
Look for currency symbols, currency codes (USD, EUR, etc.), or explicit mentions of reporting currency.
This is typically the currency of the country where the company is headquartered.

Content: {content}

Return only the 3-letter ISO currency code (e.g., "USD", "EUR", "GBP").
If unclear or not found, return "unknown".
""",
            
            "financial_year": """
From the following content about {organization_name}, identify the financial year or fiscal year format used by this organization.
Look for phrases like: "fiscal year 2023", "FY2023", "financial year ending", "year ended".

Content: {content}

Return the financial year format (e.g., "2023", "FY2023", "2022-2023").
If unclear or not found, return "unknown".
"""
        }


# Global configuration instance
config = Config()
