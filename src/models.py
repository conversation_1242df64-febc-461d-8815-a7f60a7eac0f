"""
Data models for power plant organizational details extraction.
"""
from typing import List, Optional, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class PowerPlantType(str, Enum):
    """Enumeration of power plant types."""
    COAL = "coal"
    GAS = "gas"
    NUCLEAR = "nuclear"
    SOLAR = "solar"
    WIND = "wind"
    HYDRO = "hydro"
    BIOMASS = "biomass"
    GEOTHERMAL = "geothermal"
    OIL = "oil"
    COMBINED_CYCLE = "combined_cycle"
    COGENERATION = "cogeneration"
    OTHER = "other"


class CurrencyCode(str, Enum):
    """ISO 4217 currency codes."""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    JPY = "JPY"
    CNY = "CNY"
    INR = "INR"
    CAD = "CAD"
    AUD = "AUD"
    BRL = "BRL"
    MXN = "MXN"
    # Add more as needed


class OrganizationalDetails(BaseModel):
    """Model for power plant organizational details."""
    
    cfpp_type: str = Field(
        default="",
        description="Classification/type of the power plant"
    )
    
    country_name: str = Field(
        default="",
        description="Full official name of the country where the plant is located"
    )
    
    currency_in: str = Field(
        default="",
        description="ISO 4217 currency code for financial data"
    )
    
    financial_year: str = Field(
        default="",
        description="Accounting period format (e.g., 2023-2024, FY2023)"
    )
    
    organization_name: str = Field(
        default="",
        description="Official name of the company/entity that owns/operates the plant"
    )
    
    plants_count: Optional[int] = Field(
        default=None,
        description="Total number of generation facilities owned by the organization",
        ge=0
    )
    
    plant_types: List[str] = Field(
        default_factory=list,
        description="Technologies operated by the organization"
    )
    
    ppa_flag: Optional[bool] = Field(
        default=None,
        description="Whether a Power Purchase Agreement exists for this plant"
    )
    
    province: str = Field(
        default="",
        description="Sub-national region/state/province where the plant is located"
    )
    
    @validator('currency_in')
    def validate_currency(cls, v):
        """Validate currency code format."""
        if v and len(v) != 3:
            raise ValueError('Currency code must be 3 characters')
        return v.upper() if v else v
    
    @validator('plants_count')
    def validate_plants_count(cls, v):
        """Validate plants count is reasonable."""
        if v is not None and v > 1000:
            raise ValueError('Plants count seems unreasonably high')
        return v


class SearchResult(BaseModel):
    """Model for search result from SERP API."""
    
    title: str
    url: str
    snippet: str
    rank: int
    source_type: str = "unknown"
    relevance_score: float = 0.0


class ScrapedContent(BaseModel):
    """Model for scraped web content."""
    
    url: str
    title: str
    content: str
    content_length: int
    source_type: str
    extraction_timestamp: str
    relevance_score: float = 0.0
    
    @validator('content_length', pre=True, always=True)
    def set_content_length(cls, v, values):
        """Automatically set content length."""
        return len(values.get('content', ''))


class ExtractionResult(BaseModel):
    """Model for LLM extraction result."""
    
    field_name: str
    extracted_value: Union[str, int, bool, List[str], None]
    confidence_score: float = Field(ge=0.0, le=1.0)
    source_url: str
    extraction_method: str
    
    
class PipelineConfig(BaseModel):
    """Configuration for the data retrieval pipeline."""
    
    max_search_results: int = 10
    max_scrape_pages: int = 5
    request_timeout: int = 30
    retry_attempts: int = 3
    min_content_length: int = 100
    max_content_length: int = 50000
    confidence_threshold: float = 0.7
    
    # API endpoints and keys will be loaded from environment
    serp_api_key: str = ""
    scraper_api_key: str = ""
    groq_api_key: str = ""
