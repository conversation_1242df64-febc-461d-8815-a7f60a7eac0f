"""
Main pipeline orchestrator for power plant data extraction.
"""
import asyncio
import logging
import json
from typing import Dict, List, Optional
from datetime import datetime

from src.models import OrganizationalDetails, ScrapedContent
from src.config import config
from src.serp_client import SerpAP<PERSON>lient, PowerPlantSearchOrchestrator
from src.scraper_client import <PERSON>raperAP<PERSON><PERSON>, ContentScrapeOrchestrator
from src.groq_client import GroqExtractionClient, PowerPlantDataExtractor
from src.enhanced_extractor import AdaptiveExtractor
from src.validation import CrossSourceValidator

logger = logging.getLogger(__name__)


class PowerPlantDataPipeline:
    """Main pipeline for extracting power plant organizational data."""

    def __init__(self):
        self.serp_api_key = config.pipeline.serp_api_key
        self.scraper_api_key = config.pipeline.scraper_api_key
        self.groq_api_key = ********************************************************

        # Validate API keys
        if not all([self.serp_api_key, self.scraper_api_key, self.groq_api_key]):
            raise ValueError("SERP API, Scraper API, and Groq API keys are required")

    async def extract_organizational_details(self, plant_name: str) -> OrganizationalDetails:
        """
        Extract organizational details for a power plant.

        Args:
            plant_name: Name of the power plant

        Returns:
            OrganizationalDetails object with extracted data
        """
        logger.info(f"Starting data extraction pipeline for: {plant_name}")

        # Initialize result object
        org_details = OrganizationalDetails()

        try:
            # Phase 1: Search for information
            search_results = await self._search_phase(plant_name)

            # Phase 2: Scrape content
            scraped_contents = await self._scraping_phase(search_results)

            # Phase 3: Extract data using LLM (placeholder for now)
            extracted_data = await self._extraction_phase(scraped_contents, plant_name)

            # Phase 4: Validate and populate result
            org_details = self._populate_result(extracted_data)

            logger.info(f"Pipeline completed successfully for: {plant_name}")

        except Exception as e:
            logger.error(f"Pipeline failed for {plant_name}: {e}")
            raise

        return org_details

    async def _search_phase(self, plant_name: str) -> Dict:
        """Phase 1: Search for power plant information."""
        logger.info("Phase 1: Starting search phase")

        async with SerpAPIClient(self.serp_api_key) as serp_client:
            search_orchestrator = PowerPlantSearchOrchestrator(serp_client)
            search_results = await search_orchestrator.comprehensive_search(plant_name)

        # Log search statistics
        total_results = sum(len(results) for results in search_results.values())
        logger.info(f"Search phase completed. Found {total_results} total results across {len(search_results)} categories")

        for category, results in search_results.items():
            logger.info(f"  {category}: {len(results)} results")

        return search_results

    async def _scraping_phase(self, search_results: Dict) -> List[ScrapedContent]:
        """Phase 2: Scrape content from search results."""
        logger.info("Phase 2: Starting scraping phase")

        async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
            scrape_orchestrator = ContentScrapeOrchestrator(scraper_client)
            scraped_contents = await scrape_orchestrator.scrape_search_results(
                search_results,
                max_pages_per_category=config.pipeline.max_scrape_pages
            )

        logger.info(f"Scraping phase completed. Successfully scraped {len(scraped_contents)} pages")

        # Log content statistics
        total_chars = sum(len(content.content) for content in scraped_contents)
        avg_relevance = sum(content.relevance_score for content in scraped_contents) / len(scraped_contents) if scraped_contents else 0

        logger.info(f"  Total content: {total_chars:,} characters")
        logger.info(f"  Average relevance score: {avg_relevance:.2f}")

        return scraped_contents

    async def _extraction_phase(self, scraped_contents: List[ScrapedContent], plant_name: str) -> Dict:
        """Phase 3: Extract structured data using LLM."""
        logger.info("Phase 3: Starting extraction phase")

        if not scraped_contents:
            logger.warning("No scraped content available for extraction")
            return self._get_empty_extraction_result()

        try:
            # Use adaptive extractor with enhanced validation
            adaptive_extractor = AdaptiveExtractor(self.groq_api_key)

            # Extract using adaptive strategy
            org_details = await adaptive_extractor.extract_adaptively(scraped_contents, plant_name)

            # Convert back to dict format for compatibility
            extracted_data = org_details.model_dump()

            logger.info("Enhanced extraction phase completed successfully")
            logger.info(f"Extracted data summary: {self._summarize_extraction(extracted_data)}")

            return extracted_data

        except Exception as e:
            logger.error(f"LLM extraction failed: {e}")
            logger.info("Falling back to empty extraction result")
            return self._get_empty_extraction_result()

    def _get_empty_extraction_result(self) -> Dict:
        """Get empty extraction result as fallback."""
        return {
            "cfpp_type": "",
            "country_name": "",
            "currency_in": "",
            "financial_year": "",
            "organization_name": "",
            "plants_count": None,
            "plant_types": [],
            "ppa_flag": None,
            "province": ""
        }

    def _summarize_extraction(self, extracted_data: Dict) -> str:
        """Create a summary of extracted data for logging."""
        non_empty_fields = []
        for field, value in extracted_data.items():
            if value not in [None, "", []]:
                non_empty_fields.append(field)

        return f"{len(non_empty_fields)}/{len(extracted_data)} fields extracted"

    def _populate_result(self, extracted_data: Dict) -> OrganizationalDetails:
        """Phase 4: Populate and validate result object."""
        logger.info("Phase 4: Populating result object")

        try:
            org_details = OrganizationalDetails(**extracted_data)
            logger.info("Result object populated successfully")
            return org_details
        except Exception as e:
            logger.error(f"Failed to populate result object: {e}")
            # Return empty object on validation failure
            return OrganizationalDetails()

    async def save_results(self, org_details: OrganizationalDetails, output_path: str = "org_details.json"):
        """Save extraction results to JSON file."""
        try:
            # Convert to dict and handle None values
            result_dict = org_details.model_dump()

            # Write to file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, indent=4, ensure_ascii=False)

            logger.info(f"Results saved to {output_path}")

        except Exception as e:
            logger.error(f"Failed to save results: {e}")
            raise


async def main():
    """Main function for testing the pipeline."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Test plant name
    plant_name = "Vogtle Nuclear Power Plant"

    try:
        # Initialize pipeline
        pipeline = PowerPlantDataPipeline()

        # Extract data
        org_details = await pipeline.extract_organizational_details(plant_name)

        # Save results
        await pipeline.save_results(org_details)

        # Print results
        print("\n" + "="*50)
        print("EXTRACTION RESULTS")
        print("="*50)
        print(json.dumps(org_details.model_dump(), indent=2))

    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
