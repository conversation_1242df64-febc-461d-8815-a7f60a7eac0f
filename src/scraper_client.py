"""
Web scraping client for extracting content from power plant related websites.
"""
import asyncio
import aiohttp
import logging
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from readability import Document
import html2text
from tenacity import retry, stop_after_attempt, wait_exponential

from src.models import SearchResult, ScrapedContent
from src.config import config

logger = logging.getLogger(__name__)


class ScraperAPIClient:
    """Client for web scraping using Scraper API."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "http://api.scraperapi.com"
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=config.pipeline.request_timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def scrape_url(self, url: str, render_js: bool = False) -> Optional[ScrapedContent]:
        """
        Scrape content from a URL using Scraper API.
        
        Args:
            url: URL to scrape
            render_js: Whether to render JavaScript
            
        Returns:
            ScrapedContent object or None if failed
        """
        if not self.session:
            raise RuntimeError("ScraperAPIClient must be used as async context manager")
        
        params = {
            "api_key": self.api_key,
            "url": url,
            "render": "true" if render_js else "false",
            "country_code": "us"
        }
        
        try:
            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    html_content = await response.text()
                    return self._process_html_content(url, html_content)
                else:
                    logger.warning(f"Failed to scrape {url}: HTTP {response.status}")
                    return None
                    
        except aiohttp.ClientError as e:
            logger.error(f"Scraper API request failed for {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error scraping {url}: {e}")
            return None
    
    def _process_html_content(self, url: str, html_content: str) -> ScrapedContent:
        """Process raw HTML content into structured data."""
        # Use readability to extract main content
        doc = Document(html_content)
        main_content = doc.summary()
        
        # Parse with BeautifulSoup for additional processing
        soup = BeautifulSoup(main_content, 'lxml')
        
        # Extract title
        title = ""
        title_tag = soup.find('title')
        if title_tag:
            title = title_tag.get_text().strip()
        else:
            # Try h1 tags
            h1_tag = soup.find('h1')
            if h1_tag:
                title = h1_tag.get_text().strip()
        
        # Convert to clean text
        h = html2text.HTML2Text()
        h.ignore_links = True
        h.ignore_images = True
        h.ignore_emphasis = False
        clean_text = h.handle(str(soup))
        
        # Clean up the text
        clean_text = self._clean_text_content(clean_text)
        
        # Determine source type
        source_type = self._identify_source_type(url)
        
        # Calculate relevance score
        relevance_score = self._calculate_content_relevance(clean_text)
        
        return ScrapedContent(
            url=url,
            title=title,
            content=clean_text,
            content_length=len(clean_text),
            source_type=source_type,
            extraction_timestamp=datetime.now().isoformat(),
            relevance_score=relevance_score
        )
    
    def _clean_text_content(self, text: str) -> str:
        """Clean and normalize text content."""
        # Remove excessive whitespace
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line and len(line) > 10:  # Filter out very short lines
                cleaned_lines.append(line)
        
        # Join lines and limit content length
        cleaned_text = '\n'.join(cleaned_lines)
        
        if len(cleaned_text) > config.pipeline.max_content_length:
            cleaned_text = cleaned_text[:config.pipeline.max_content_length] + "..."
        
        return cleaned_text
    
    def _identify_source_type(self, url: str) -> str:
        """Identify source type based on URL patterns."""
        url_lower = url.lower()
        
        for source_type, indicators in config.source_type_indicators.items():
            if any(indicator in url_lower for indicator in indicators):
                return source_type
        
        return "other"
    
    def _calculate_content_relevance(self, content: str) -> float:
        """Calculate relevance score for content."""
        content_lower = content.lower()
        score = 0.0
        
        # High relevance keywords
        for keyword in config.content_relevance_keywords["high_relevance"]:
            count = content_lower.count(keyword)
            score += count * 0.1
        
        # Medium relevance keywords
        for keyword in config.content_relevance_keywords["medium_relevance"]:
            count = content_lower.count(keyword)
            score += count * 0.05
        
        # Organizational keywords
        for keyword in config.content_relevance_keywords["organizational"]:
            count = content_lower.count(keyword)
            score += count * 0.08
        
        # Location keywords
        for keyword in config.content_relevance_keywords["location"]:
            count = content_lower.count(keyword)
            score += count * 0.06
        
        return min(score, 1.0)  # Cap at 1.0


class ContentScrapeOrchestrator:
    """Orchestrates content scraping from search results."""
    
    def __init__(self, scraper_client: ScraperAPIClient):
        self.scraper_client = scraper_client
    
    async def scrape_search_results(
        self, 
        search_results: Dict[str, List[SearchResult]],
        max_pages_per_category: int = 5
    ) -> List[ScrapedContent]:
        """
        Scrape content from prioritized search results.
        
        Args:
            search_results: Categorized search results from SERP API
            max_pages_per_category: Maximum pages to scrape per category
            
        Returns:
            List of ScrapedContent objects
        """
        # Prioritize URLs for scraping
        prioritized_urls = self._prioritize_urls_for_scraping(
            search_results, 
            max_pages_per_category
        )
        
        logger.info(f"Starting content scraping for {len(prioritized_urls)} URLs")
        
        scraped_contents = []
        
        # Scrape URLs with rate limiting
        for i, (url, source_info) in enumerate(prioritized_urls):
            try:
                logger.info(f"Scraping {i+1}/{len(prioritized_urls)}: {url}")
                
                # Determine if JS rendering is needed
                render_js = self._needs_js_rendering(url)
                
                content = await self.scraper_client.scrape_url(url, render_js)
                
                if content and len(content.content) >= config.pipeline.min_content_length:
                    # Update source type from search result if available
                    if source_info:
                        content.source_type = source_info.get('source_type', content.source_type)
                    
                    scraped_contents.append(content)
                    logger.info(f"Successfully scraped {url} ({len(content.content)} chars)")
                else:
                    logger.warning(f"Content too short or failed for {url}")
                
                # Rate limiting
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Failed to scrape {url}: {e}")
                continue
        
        # Sort by relevance and source priority
        scraped_contents.sort(
            key=lambda x: (
                config.url_priority_weights.get(x.source_type, 0),
                x.relevance_score
            ),
            reverse=True
        )
        
        logger.info(f"Content scraping completed. {len(scraped_contents)} pages successfully scraped")
        
        return scraped_contents
    
    def _prioritize_urls_for_scraping(
        self, 
        search_results: Dict[str, List[SearchResult]],
        max_per_category: int
    ) -> List[tuple]:
        """Prioritize URLs for scraping based on relevance and source type."""
        prioritized_urls = []
        
        # Category priority order
        category_priority = [
            "basic_discovery",
            "organizational", 
            "technical_details",
            "location_details",
            "ppa_details"
        ]
        
        for category in category_priority:
            if category in search_results:
                results = search_results[category]
                
                # Sort by combined score
                sorted_results = sorted(
                    results,
                    key=lambda x: (
                        config.url_priority_weights.get(x.source_type, 0),
                        x.relevance_score
                    ),
                    reverse=True
                )
                
                # Take top results from this category
                for result in sorted_results[:max_per_category]:
                    url_info = {
                        'source_type': result.source_type,
                        'relevance_score': result.relevance_score,
                        'category': category
                    }
                    prioritized_urls.append((result.url, url_info))
        
        # Remove duplicates while preserving order
        seen_urls = set()
        unique_urls = []
        for url, info in prioritized_urls:
            if url not in seen_urls:
                seen_urls.add(url)
                unique_urls.append((url, info))
        
        return unique_urls
    
    def _needs_js_rendering(self, url: str) -> bool:
        """Determine if URL likely needs JavaScript rendering."""
        js_indicators = [
            "react", "angular", "vue", "spa", "app",
            "dashboard", "portal", "interactive"
        ]
        
        url_lower = url.lower()
        return any(indicator in url_lower for indicator in js_indicators)
