"""
Test script for Phase 5: Enhanced Data Validation features.
"""
import asyncio
import logging
import os
from datetime import datetime
from src.enhanced_extractor import AdaptiveExtractor, EnhancedDataExtractor
from src.validation import DataValidator, CrossSourceValidator
from src.models import ScrapedContent, ExtractionResult

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_validation_features():
    """Test the enhanced validation features."""
    print("="*60)
    print("PHASE 5: ENHANCED DATA VALIDATION TEST")
    print("="*60)
    
    # Check if Groq API key is available
    groq_key = os.getenv('GROQ_API_KEY')
    if not groq_key:
        print("❌ GROQ_API_KEY not configured. Skipping validation tests.")
        return
    
    print("✅ Testing validation components...")
    
    # Test 1: Data Validator
    await test_data_validator()
    
    # Test 2: Cross-Source Validator
    await test_cross_source_validator()
    
    # Test 3: Enhanced Extractor
    await test_enhanced_extractor(groq_key)
    
    # Test 4: Adaptive Extractor
    await test_adaptive_extractor(groq_key)


async def test_data_validator():
    """Test the DataValidator class."""
    print("\n" + "="*40)
    print("TEST 1: Data Validator")
    print("="*40)
    
    try:
        validator = DataValidator()
        
        # Create mock extraction results with conflicts
        mock_results = [
            ExtractionResult(
                field_name="organization_name",
                extracted_value="Georgia Power Company",
                confidence_score=0.9,
                source_url="source1.com",
                extraction_method="groq"
            ),
            ExtractionResult(
                field_name="organization_name", 
                extracted_value="Georgia Power Co.",
                confidence_score=0.8,
                source_url="source2.com",
                extraction_method="groq"
            ),
            ExtractionResult(
                field_name="cfpp_type",
                extracted_value="nuclear",
                confidence_score=0.9,
                source_url="source1.com",
                extraction_method="groq"
            ),
            ExtractionResult(
                field_name="country_name",
                extracted_value="United States",
                confidence_score=0.8,
                source_url="source1.com",
                extraction_method="groq"
            )
        ]
        
        # Test validation
        result = validator.validate_extraction_results(mock_results, "Test Plant")
        
        print("✅ Data validation test passed")
        print(f"   Validated data: {result['data']}")
        print(f"   Validation summary: {result['validation_summary']}")
        
    except Exception as e:
        print(f"❌ Data validator test failed: {e}")


async def test_cross_source_validator():
    """Test the CrossSourceValidator class."""
    print("\n" + "="*40)
    print("TEST 2: Cross-Source Validator")
    print("="*40)
    
    try:
        cross_validator = CrossSourceValidator()
        
        # Create mock extraction attempts
        mock_attempts = [
            {
                "method": "combined_content",
                "extraction_results": [
                    ExtractionResult(
                        field_name="organization_name",
                        extracted_value="Test Company",
                        confidence_score=0.8,
                        source_url="combined",
                        extraction_method="groq"
                    )
                ]
            }
        ]
        
        # Test cross-source validation
        result = await cross_validator.validate_multi_source_extraction(
            mock_attempts, "Test Plant"
        )
        
        print("✅ Cross-source validation test passed")
        print(f"   Result type: {type(result)}")
        print(f"   Organization name: {result.organization_name}")
        
    except Exception as e:
        print(f"❌ Cross-source validator test failed: {e}")


async def test_enhanced_extractor(groq_key: str):
    """Test the EnhancedDataExtractor class."""
    print("\n" + "="*40)
    print("TEST 3: Enhanced Extractor")
    print("="*40)
    
    try:
        enhanced_extractor = EnhancedDataExtractor(groq_key)
        
        # Create mock scraped content
        mock_content = [
            ScrapedContent(
                url="https://example.com/test1",
                title="Test Plant Information",
                content="Test Plant is a nuclear power plant owned by Test Company in the United States.",
                content_length=100,
                source_type="company_official",
                extraction_timestamp=datetime.now().isoformat(),
                relevance_score=0.9
            ),
            ScrapedContent(
                url="https://example.com/test2",
                title="Test Plant Details",
                content="The Test Plant facility generates electricity using nuclear technology.",
                content_length=80,
                source_type="government_database",
                extraction_timestamp=datetime.now().isoformat(),
                relevance_score=0.8
            )
        ]
        
        print("🧪 Testing enhanced extraction (this may take a moment)...")
        
        # Test enhanced extraction
        result = await enhanced_extractor.extract_with_validation(mock_content, "Test Plant")
        
        print("✅ Enhanced extractor test passed")
        print(f"   Result type: {type(result)}")
        print(f"   Organization: {result.organization_name}")
        print(f"   Plant type: {result.cfpp_type}")
        
    except Exception as e:
        print(f"❌ Enhanced extractor test failed: {e}")


async def test_adaptive_extractor(groq_key: str):
    """Test the AdaptiveExtractor class."""
    print("\n" + "="*40)
    print("TEST 4: Adaptive Extractor")
    print("="*40)
    
    try:
        adaptive_extractor = AdaptiveExtractor(groq_key)
        
        # Test with different content scenarios
        scenarios = [
            {
                "name": "High Quality Content",
                "content": [
                    ScrapedContent(
                        url="https://company.com/plant",
                        title="Official Plant Info",
                        content="Our nuclear power plant is operated by Major Energy Corp in California, USA. " * 50,
                        content_length=3000,
                        source_type="company_official",
                        extraction_timestamp=datetime.now().isoformat(),
                        relevance_score=0.9
                    ),
                    ScrapedContent(
                        url="https://gov.com/database",
                        title="Government Database",
                        content="Nuclear facility owned by Major Energy Corp, capacity 1000 MW. " * 30,
                        content_length=2000,
                        source_type="government_database",
                        extraction_timestamp=datetime.now().isoformat(),
                        relevance_score=0.8
                    ),
                    ScrapedContent(
                        url="https://news.com/article",
                        title="News Article",
                        content="The plant in California generates clean nuclear energy. " * 20,
                        content_length=1000,
                        source_type="news_article",
                        extraction_timestamp=datetime.now().isoformat(),
                        relevance_score=0.7
                    )
                ]
            },
            {
                "name": "Limited Content",
                "content": [
                    ScrapedContent(
                        url="https://example.com/basic",
                        title="Basic Info",
                        content="Small wind farm in Texas.",
                        content_length=25,
                        source_type="other",
                        extraction_timestamp=datetime.now().isoformat(),
                        relevance_score=0.4
                    )
                ]
            }
        ]
        
        for scenario in scenarios:
            print(f"\n🧪 Testing scenario: {scenario['name']}")
            
            try:
                result = await adaptive_extractor.extract_adaptively(
                    scenario['content'], "Test Plant"
                )
                
                print(f"   ✅ Scenario '{scenario['name']}' passed")
                print(f"   Organization: {result.organization_name or 'Not found'}")
                print(f"   Plant type: {result.cfpp_type or 'Not found'}")
                
            except Exception as e:
                print(f"   ❌ Scenario '{scenario['name']}' failed: {e}")
        
        print("\n✅ Adaptive extractor test completed")
        
    except Exception as e:
        print(f"❌ Adaptive extractor test failed: {e}")


async def test_component_imports():
    """Test that all Phase 5 components can be imported."""
    print("\n" + "="*40)
    print("TEST: Component Imports")
    print("="*40)
    
    try:
        from src.validation import DataValidator, CrossSourceValidator
        from src.enhanced_extractor import EnhancedDataExtractor, AdaptiveExtractor
        
        print("✅ All Phase 5 components imported successfully")
        
        # Test instantiation
        validator = DataValidator()
        cross_validator = CrossSourceValidator()
        
        print("✅ All Phase 5 components instantiated successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Component import test failed: {e}")
        return False


async def main():
    """Main test function."""
    try:
        # Test imports first
        imports_ok = await test_component_imports()
        
        if imports_ok:
            # Run validation tests
            await test_validation_features()
        else:
            print("\n❌ Component imports failed. Skipping validation tests.")
        
        print("\n" + "="*60)
        print("PHASE 5 TESTING COMPLETED")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.error(f"Test suite failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
