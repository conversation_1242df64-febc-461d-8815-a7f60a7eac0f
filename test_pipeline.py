"""
Test script for the power plant data extraction pipeline.
"""
import asyncio
import logging
import os
from src.pipeline import PowerPlantDataPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_pipeline_basic():
    """Test basic pipeline functionality."""
    print("="*60)
    print("POWER PLANT DATA EXTRACTION PIPELINE TEST")
    print("="*60)
    
    # Check if API keys are configured
    serp_key = os.getenv('SERP_API_KEY')
    scraper_key = os.getenv('SCRAPER_API_KEY')
    
    if not serp_key or not scraper_key:
        print("\n❌ API keys not configured!")
        print("Please set SERP_API_KEY and SCRAPER_API_KEY in your .env file")
        print("Copy .env.example to .env and add your API keys")
        return
    
    print(f"\n✅ API keys configured")
    print(f"   SERP API: {'*' * (len(serp_key) - 4) + serp_key[-4:]}")
    print(f"   Scraper API: {'*' * (len(scraper_key) - 4) + scraper_key[-4:]}")
    
    # Test plants
    test_plants = [
        "Vogtle Nuclear Power Plant",
        "Palo Verde Nuclear Generating Station", 
        "Hoover Dam"
    ]
    
    try:
        pipeline = PowerPlantDataPipeline()
        
        for i, plant_name in enumerate(test_plants, 1):
            print(f"\n{'='*40}")
            print(f"TEST {i}: {plant_name}")
            print(f"{'='*40}")
            
            try:
                # Extract data
                org_details = await pipeline.extract_organizational_details(plant_name)
                
                # Save results with plant-specific filename
                output_file = f"test_results_{plant_name.lower().replace(' ', '_')}.json"
                await pipeline.save_results(org_details, output_file)
                
                print(f"\n✅ Test {i} completed successfully")
                print(f"   Results saved to: {output_file}")
                
                # Print summary
                print(f"\n📊 EXTRACTED DATA SUMMARY:")
                print(f"   Organization: {org_details.organization_name or 'Not found'}")
                print(f"   Plant Type: {org_details.cfpp_type or 'Not found'}")
                print(f"   Country: {org_details.country_name or 'Not found'}")
                print(f"   Province: {org_details.province or 'Not found'}")
                
            except Exception as e:
                print(f"\n❌ Test {i} failed: {e}")
                logger.error(f"Test failed for {plant_name}: {e}")
                continue
    
    except Exception as e:
        print(f"\n❌ Pipeline initialization failed: {e}")
        logger.error(f"Pipeline initialization failed: {e}")


async def test_individual_components():
    """Test individual pipeline components."""
    print("\n" + "="*60)
    print("COMPONENT TESTING")
    print("="*60)
    
    # Test imports
    try:
        from src.models import OrganizationalDetails, SearchResult, ScrapedContent
        from src.config import config
        from src.serp_client import SerpAPIClient
        from src.scraper_client import ScraperAPIClient
        
        print("✅ All imports successful")
        
        # Test model creation
        org_details = OrganizationalDetails()
        print("✅ OrganizationalDetails model creation successful")
        
        # Test configuration
        print(f"✅ Configuration loaded:")
        print(f"   Max search results: {config.pipeline.max_search_results}")
        print(f"   Max scrape pages: {config.pipeline.max_scrape_pages}")
        print(f"   Confidence threshold: {config.pipeline.confidence_threshold}")
        
        # Test search query templates
        templates = config.search_query_templates
        print(f"✅ Search templates loaded: {len(templates)} categories")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        logger.error(f"Component test failed: {e}")
        return False


if __name__ == "__main__":
    async def run_tests():
        # Test components first
        components_ok = await test_individual_components()
        
        if components_ok:
            # Run pipeline tests
            await test_pipeline_basic()
        else:
            print("\n❌ Component tests failed. Skipping pipeline tests.")
    
    asyncio.run(run_tests())
